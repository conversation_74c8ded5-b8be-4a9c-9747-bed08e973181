/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  swcMinify: true,
  basePath: "/cube",
  env: {
    basePath: "/cube",
  },
  // async rewrites() {
  //   return {
  //     fallback: [
  //       {
  //         source: "/api/:path*",
  //         destination: `${process.env.NEXT_PUBLIC_API_SMART}/:path*`, // Proxy to Backend
  //       },
  //       {
  //         source: "/eRecruitWS/:path*",
  //         destination: `${process.env.NEXT_PUBLIC_API_ERECRUIT}/:path*`, // Proxy to Backend
  //       },
  //       {
  //         source: "/cube-api/:path*",
  //         destination: `${process.env.NEXT_PUBLIC_API_PORTAL}/:path*`, // Proxy to Backend
  //       },
  //       {
  //         source: "/smart2/api/:path*",
  //         destination: `${process.env.NEXT_PUBLIC_API_LEADS}/:path*`, // Proxy to Backend
  //       },
  //     ],
  //   };
  // },
  webpack5: true,
  webpack: (config) => {
    config.resolve.fallback = { fs: false, path: false, stream: false, zlib: false };
    return config;
  },
};

module.exports = nextConfig;
