const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const port = process.env.PORT || 3000;

app.use('/cube-api', createProxyMiddleware({
    target: 'https://uat-smart.fwd.com.vn/cube/cube-api', // target host with the same base path
    changeOrigin: true, // needed for virtual hosted sites
    logger: console,
}));

// mount `exampleProxy` in web server
app.use('/api', createProxyMiddleware({
    target: 'https://uat-smart.fwd.com.vn/cube/api', // target host with the same base path
    changeOrigin: true, // needed for virtual hosted sites
    logger: console,
    cookieDomainRewrite: ''
}));

app.use('/smart2', createProxyMiddleware({
    target: 'https://uat-smart.fwd.com.vn/cube/smart2/api', // target host with the same base path
    changeOrigin: true, // needed for virtual hosted sites
}));
app.use('/eRecruitWS', createProxyMiddleware({
    target: 'https://uat-smart.fwd.com.vn/cube/eRecruitWS', // target host with the same base path
    changeOrigin: true, // needed for virtual hosted sites
}));

app.listen(port, function () {
    console.log(`Proxy server started on port ${port}`)
})
