services:
  gluetun:
    image: qmcgaw/gluetun
    hostname: gluetun
    cap_add:
      - NET_ADMIN
    environment:
      - VPN_SERVICE_PROVIDER=custom
      - VPN_TYPE=wireguard
      - WIREGUARD_ENDPOINT_IP=**************
      - WIREGUARD_ENDPOINT_PORT=51820
      - WIREGUARD_PUBLIC_KEY=${WIREGUARD_PUBLIC_KEY}
      - WIREGUARD_PRIVATE_KEY=${WIREGUARD_PRIVATE_KEY}
      - WIREGUARD_PRESHARED_KEY=${WIREGUARD_PRESHARED_KEY}
      - WIREGUARD_ADDRESSES=*********/24
    container_name: gluetun
    volumes:
      - /gluetun
    ports:
      - 3013:3013/tcp
      - 3003:3003/tcp

  proxy:
    build:
      context: .
      dockerfile: Dockerfile_proxy
    container_name: node_proxy
    environment:
      - PORT=3003
    network_mode: container:gluetun
    depends_on:
      gluetun:
        condition: service_healthy
    security_opt:
      - no-new-privileges:true
    restart: always

  web:
    build:
      context: .
      dockerfile: Dockerfile_web
    container_name: fwd-cube-fe
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Ho_Chi_Minh
      - UMASK=00
    volumes:
      - ./:/app
      - /app/node_modules
      - /app/.next
    network_mode: container:gluetun
    depends_on:
      gluetun:
        condition: service_healthy
    security_opt:
      - no-new-privileges:true
    restart: always
