{"compilerOptions": {"baseUrl": "src", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "strictNullChecks": false, "jsx": "preserve", "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src"], "exclude": ["node_modules"]}