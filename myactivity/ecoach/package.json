{"name": "ecoach", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "build:s3": "tsc && vite build --mode production"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@reduxjs/toolkit": "^1.9.3", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-saga": "^1.2.3", "styled-components": "^5.3.9", "axios": "^1.3.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "lottie-react": "^2.4.0", "react-toastify": "^9.1.2", "query-string": "^8.1.0", "crypto-js": "^4.1.1", "md5": "^2.3.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/lodash": "^4.14.191", "@types/styled-components": "^5.1.26", "@types/crypto-js": "^4.1.1", "@types/md5": "^2.3.2", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^5.0.2", "vite": "^4.3.2"}}