import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@screens': path.resolve(__dirname, './src/screens'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@api': path.resolve(__dirname, './src/api'),
      '@types': path.resolve(__dirname, './src/types'),
      '@custom-types': path.resolve(__dirname, './src/types/custom-types'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@styles': path.resolve(__dirname, './src/styles'),
      '@store': path.resolve(__dirname, './src/store'),
      '@utils': path.resolve(__dirname, './src/utils')
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          redux: ['@reduxjs/toolkit', 'react-redux', 'redux-saga'],
          ui: ['styled-components', 'lottie-react']
        }
      }
    }
  },
  base: './',
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/ecoach-api': {
        target: process.env.VITE_API_ECOACH || 'https://uat.guru.fwd.com/trainer',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ecoach-api/, ''),
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Add any additional headers if needed
            proxyReq.setHeader('country', 'vn');
          });
        }
      }
    }
  },
  preview: {
    port: 3000,
    host: true
  }
})
