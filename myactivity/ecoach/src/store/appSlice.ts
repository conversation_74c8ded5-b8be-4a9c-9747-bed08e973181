import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppState, User } from "@types/ecoach";

const initialState: AppState = {
  user: {
    channel: "",
    email: "",
    id: undefined,
    name: "",
    username: "",
    designation: "",
    designationDesc: "",
  },
  loading: false,
};

const appSlice = createSlice({
  name: "app",
  initialState: initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    resetApp: () => {
      return initialState;
    },
  },
});

export const { setUser, setLoading, resetApp } = appSlice.actions;

export default appSlice.reducer;
