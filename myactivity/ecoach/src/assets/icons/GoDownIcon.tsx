import React from "react";
import { SvgIconProps } from "./SvgIconProps";

const GoDownIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.739 3.855a1.5 1.5 0 010 2.12l-7.645 7.645-7.645-7.645a1.5 1.5 0 010-2.12l.355-.355 7.295 7.295L19.394 3.5l.345.355zm-.355 6.5l-7.295 7.295-7.295-7.295-.355.355a1.5 1.5 0 000 2.12l7.645 7.645 7.645-7.645a1.5 1.5 0 000-2.12l-.345-.355z"
        fill={props.fill}
      />
    </svg>
  );
};

export default GoDownIcon;
