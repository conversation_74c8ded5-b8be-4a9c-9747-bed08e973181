import React from "react";
import { SvgIconProps } from "./SvgIconProps";

const CloseIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <g id="close">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.414 5.79263L19.0605 5.43913C18.475 4.85362 17.525 4.85362 16.9395 5.43913L11.707 10.6716L6.475 5.43913C5.889 4.85362 4.939 4.85362 4.3535 5.43913L4 5.79263L10.293 12.0856L4 18.3786L4.3535 18.7321C4.939 19.3176 5.889 19.3176 6.475 18.7321L11.707 13.4996L16.9395 18.7321C17.525 19.3176 18.475 19.3176 19.0605 18.7321L19.414 18.3786L13.121 12.0856L19.414 5.79263Z"
          fill={props.fill}
        />
      </g>
    </svg>
  );
};

export default CloseIcon;
