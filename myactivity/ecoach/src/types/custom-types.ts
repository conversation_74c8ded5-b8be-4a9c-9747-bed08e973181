export interface IconProperty {
  width?: number | string;
  height?: number | string;
  color?: string;
  className?: string;
  onClick?: () => void;
}

export enum TextAlign {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  JUSTIFY = 'justify',
}

export interface ModalSize {
  width?: string;
  height?: string;
  maxWidth?: string;
  maxHeight?: string;
}

export interface BaseModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  title?: string;
  children?: React.ReactNode;
  size?: ModalSize;
  backgroundMobile?: string;
}
