export const DEFAULT_QUICKFIRE_COMPLETED_HEARTS = 40;
export const ECOACH_COUNTRY = "vn";

export const DEFAULT_PRODUCTS: EcoachProductConfig[] = [
  {
    product_code: "set_for_life",
    product_name: "Set for life",
    product_description: "lifeSetDes",
    order: 0.0,
    product_image_base64: "",
    product_image: "sellSetForLifeProductImage",
    clickable: true,
  },
  {
    product_code: "set_for_health",
    product_name: "Set for health",
    product_description: "healthSetDes",
    order: 1.0,
    product_image_base64: "",
    product_image: "sellSetForHealthProductImage",
    clickable: true,
  },
];

export type EcoachState = {
  isQuickfire: boolean;
  isTrainerGuruEnabled: boolean;
  quickfireVideoUrl?: string | null;
  homepageBackground?: HomepageBackgroundType;
  quickfireCompletedHearts: number;
  productConfig?: Record<string, EcoachProductConfig[]>;
  quickfireProductConfig?: Record<string, EcoachProductConfig[]>;
  cubeToken: string;
  conversationsData: {
    [key: string]: ConversationData;
  };
};

export type EcoachProductConfig = {
  product_name: string;
  product_description: string;
  product_code: string;
  policy_type?: string;
  order: number;
  product_image?: string;
  product_image_base64?: string;
  clickable: boolean;
  channel?: string;
};

export type EcoachConfigurationData = {
  isQuickfire: boolean;
  isTrainerGuruEnabled: boolean;
  quickfireVideoUrl: string;
  homepageBackground?: HomepageBackgroundType;
  quickfireCompletedHearts: number;
  productConfig?: Record<string, EcoachProductConfig[]>;
  quickfireProductConfig?: Record<string, EcoachProductConfig[]>;
};

export enum ProductFlowType {
  QUICKFIRE = "quickfire",
  FULL_EXPERIENCE = "full_experience",
}

export type HomepageBackgroundType = {
  avatar_image_tablet: string;
  avatar_image_mobile: string;
  background_image_tablet: string;
  background_image_mobile: string;
};

export type EcoachConfigItem = {
  update_at: string;
  value: any;
  created_at: string;
  key: string;
  uid: string;
};

export type MaintenanceStatus = {
  start_datetime: string;
  end_datetime: string;
};

export type SkillDetail = {
  reason: string;
  improvement: string;
  assessment: string;
  excerpt: string;
  atom_summary: string;
  try_next_time: string;
};

type SkillSetDetails = {
  applied_product_knowledge: { [key: string]: SkillDetail }[];
  customer_relationship: { [key: string]: SkillDetail }[];
  objection_handling_closing: { [key: string]: SkillDetail }[];
  communication_skills: { [key: string]: SkillDetail }[];
  customer_discovery: { [key: string]: SkillDetail }[];
};

export type Report =
  | {
      overall_score: string;
      applied_product_knowledge_score: string;
      customer_discovery_score: string;
      customer_relationship_score: string;
      objection_handling_closing_score: string;
      communication_skills_score: string;
      well_done_comment: string;
      improvement_comment: string;
      skill_set_details: SkillSetDetails;
    }
  | Record<string, never>;

export type ConversationData = {
  report_is_ready: string;
  conversation_id: string;
  created_at: string;
  datetime: string;
  difficulty: string;
  duration: number;
  product_selection?: string;
  session_number?: number;
  report: Report;
};

export type ConversationHistoryResponse = {
  items: ConversationData[];
  last_evaluated_key: string;
  order: "asc" | "desc";
  agent_id: string;
  has_more: boolean;
  total_count: number;
};

export enum ConversationType {
  CUSTOMER = "customer",
  PRODUCT_TRAINER = "product_trainer",
  QUIZ = "quiz",
  QUICKFIRE = "quickfire",
  HK_EAIC = "hk-eaic",
  UNKNOWN = "unknown",
}

export type ConversationFeedBackType = "ROLE_PLAY" | "REPORT" | "OVERALL";

export type ConversationFeedbackPayload = {
  conversation_id?: string;
  star: number;
  feedback_type: ConversationFeedBackType;
  comments: string[];
};

export enum DifficultType {
  Beginner = "Beginner",
  Expert = "Expert",
}

export enum MissionCompleteScore {
  Beginner = 80,
  Expert = 140,
}
