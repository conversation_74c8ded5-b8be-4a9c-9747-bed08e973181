import styled, { keyframes } from 'styled-components';

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

export const FadeInAnimation = styled.div`
  animation: ${fadeIn} 0.3s ease-in-out;
`;

export const FadeOutAnimation = styled.div`
  animation: ${fadeOut} 0.3s ease-in-out;
`;

export const FadeContainer = styled.div<{ isVisible: boolean }>`
  animation: ${props => props.isVisible ? fadeIn : fadeOut} 0.3s ease-in-out;
  opacity: ${props => props.isVisible ? 1 : 0};
`;

// Aliases for backward compatibility
export const FadeStyled = FadeInAnimation;
export const FadeReverseStyled = FadeOutAnimation;
