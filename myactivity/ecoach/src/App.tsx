import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from "react-redux";
import { ThemeProvider } from "styled-components";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { store } from "@store/index";
import { theme } from "@styles/theme";
import { GlobalStyle } from "@styles/GlobalStyle";

// Import screens
import HomePage from "@screens/home/<USER>";
import ProductSelection from "@screens/product-selection/ProductSelection";
import ProductLevelSelection from "@screens/product-level-selection/ProductLevelSelection";
import VideoCall from "@screens/video-call/VideoCall";
import SessionHistory from "@screens/session-history/SessionHistory";
import Report from "@screens/report/Report";
import ReportDetails from "@screens/report-details/ReportDetails";
import Guidelines from "@screens/guidelines/Guidelines";

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <GlobalStyle />
        <Router>
          <Routes>
            {/* Main ecoach routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/product-selection" element={<ProductSelection />} />
            <Route path="/product-level-selection" element={<ProductLevelSelection />} />
            <Route path="/video-call" element={<VideoCall />} />
            <Route path="/session-history" element={<SessionHistory />} />
            <Route path="/report" element={<Report />} />
            <Route path="/report/details/:conversationId" element={<ReportDetails />} />
            <Route path="/guidelines" element={<Guidelines />} />
            
            {/* Redirect any unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </ThemeProvider>
    </Provider>
  );
}

export default App;
