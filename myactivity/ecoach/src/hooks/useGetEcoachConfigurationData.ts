import { useEffect, useState } from "react";
import { useAppDispatch } from "../store/index";
import { setupConfiguration } from "../store/ecoachSlice";
import { getConfigurationData } from "../api/ecoach";
import { useActionApi } from "./useActionApi";
import {
  DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
  EcoachConfigItem,
  EcoachConfigurationData,
  EcoachProductConfig,
  HomepageBackgroundType,
} from "../types/ecoach";

export const useGetEcoachConfigurationData = (enabled: boolean) => {
  const dispatch = useAppDispatch();
  const [ecoachConfig, setEcoachConfig] = useState<EcoachConfigurationData>();

  const actionGetConfigurationData = useActionApi(getConfigurationData);

  useEffect(() => {
    if (!enabled) {
      return;
    }

    actionGetConfigurationData.execute().then((response) => {
      const { data } = response;

      const oldHomeConfig = data?.find(
        (item: EcoachConfigItem) => item.key === "homepage_config" && item.value !== "quickfire"
      );
      const homepageBackgroundData = data?.find((item: EcoachConfigItem) => item.key === "homepage_background");
      let homepageBackground;
      if (homepageBackgroundData?.value) {
        homepageBackground = homepageBackgroundData.value.find(
          (bg: HomepageBackgroundType) =>
            bg.background_image_tablet && bg.background_image_mobile && bg.avatar_image_mobile && bg.avatar_image_tablet
        );
      }

      const quickfireUrl = data?.find((item: EcoachConfigItem) => item.key === "trainer_guru_tutorial_video");

      const quickfireCompletedHearts = data?.find(
        (item: EcoachConfigItem) => item.key === "quickfire_completed_hearts"
      );
      const showTG = data?.find((item: EcoachConfigItem) => item.key === "show_trainer_guru");
      const isTrainerGuruEnabled = showTG?.value;
      const productConfig: Record<string, EcoachProductConfig[]> = {} as Record<string, EcoachProductConfig[]>;
      const quickfireProductConfig: Record<string, EcoachProductConfig[]> = {} as Record<string, EcoachProductConfig[]>;

      const config = data?.find((item: EcoachConfigItem) => item.key === `product_selection`);

      if (config) {
        productConfig["vn"] = config.value as EcoachProductConfig[];
      }

      const qfConfig = data?.find((item: EcoachConfigItem) => item.key === `quickfire_product_selection`);

      if (qfConfig) {
        quickfireProductConfig["vn"] = qfConfig.value as EcoachProductConfig[];
      }

      const appConfig = {
        isQuickfire: !oldHomeConfig,
        isTrainerGuruEnabled: !!isTrainerGuruEnabled,
        quickfireVideoUrl: quickfireUrl ? quickfireUrl?.value : null,
        homepageBackground,
        quickfireCompletedHearts:
          parseInt(quickfireCompletedHearts?.value || `${DEFAULT_QUICKFIRE_COMPLETED_HEARTS}`, 10) ??
          DEFAULT_QUICKFIRE_COMPLETED_HEARTS,
        productConfig,
        quickfireProductConfig,
      };

      dispatch(setupConfiguration(appConfig));
      setEcoachConfig(appConfig);
    }).catch((error) => {
      console.error("Failed to get ecoach configuration:", error);
    });
  }, [enabled]);

  return { ecoachConfig };
};
