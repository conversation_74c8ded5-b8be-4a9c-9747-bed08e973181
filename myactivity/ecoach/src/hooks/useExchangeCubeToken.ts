import { useEffect, useState } from "react";
import { useAppDispatch } from "@store/index";
import { setToken } from "@store/ecoachSlice";
import { getVerifyCubeToken } from "@api/ecoach";
import useActionApi from "./useActionApi";

export const useExchangeCubeToken = () => {
  const dispatch = useAppDispatch();
  const [cubeToken, setCubeToken] = useState("");

  const actionVerifyCubeToken = useActionApi<any, { jwt_token: string }>(getVerifyCubeToken);

  useEffect(() => {
    actionVerifyCubeToken({
      loading: {
        type: "local",
        name: "",
      },
    }).then((response) => {
      const { data } = response;

      dispatch(setToken(data.jwt_token));
      setCubeToken(data.jwt_token);
    }).catch((error) => {
      console.error("Failed to exchange cube token:", error);
    });
  }, []);

  return { cubeToken };
};
