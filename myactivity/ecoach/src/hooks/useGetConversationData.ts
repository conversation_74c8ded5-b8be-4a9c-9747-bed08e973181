import { useState, useEffect } from 'react';
import { ConversationData } from '../types/ecoach-custom';

interface UseGetConversationDataReturn {
  data: ConversationData | null;
  loading: boolean;
  error: string | null;
}

export const useGetConversationData = (conversationId: string): UseGetConversationDataReturn => {
  const [data, setData] = useState<ConversationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!conversationId) return;

    const fetchConversationData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Mock API call - replace with actual API call
        const response = await fetch(`/ecoach-api/conversation/${conversationId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch conversation data');
        }
        
        const conversationData = await response.json();
        setData(conversationData);
      } catch (err: any) {
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchConversationData();
  }, [conversationId]);

  return { data, loading, error };
};
