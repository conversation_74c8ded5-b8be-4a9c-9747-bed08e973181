import { useState, useEffect } from "react";
import { ConversationData } from "../types/ecoach";

export const useReportHistoryLatest = (limit: number = 5) => {
  const [data, setData] = useState<ConversationData[] | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Mock data for now - replace with actual API call later
    const mockData: ConversationData[] = [
      {
        conversation_id: "1",
        product_name: "Set For Health",
        overall_score: 85,
        created_at: "2024-01-15T10:30:00Z",
        session_number: 1,
        conversation_type: "quickfire",
        status: "completed"
      },
      {
        conversation_id: "2", 
        product_name: "Set For Life",
        overall_score: 92,
        created_at: "2024-01-14T14:20:00Z",
        session_number: 2,
        conversation_type: "full",
        status: "completed"
      },
      {
        conversation_id: "3",
        product_name: "Set For Health",
        overall_score: 78,
        created_at: "2024-01-13T09:15:00Z", 
        session_number: 3,
        conversation_type: "quickfire",
        status: "completed"
      }
    ];

    setLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      setData(mockData.slice(0, limit));
      setLoading(false);
    }, 500);
  }, [limit]);

  return { data, loading };
};
