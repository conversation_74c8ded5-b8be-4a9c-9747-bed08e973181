import { useCallback, useEffect, useState } from "react";
import axios, { AxiosResponse } from "axios";

interface LoadingProps {
  type?: "local" | "global";
  name?: string;
}

interface ActionApiParams<P> {
  body?: P;
  loading?: LoadingProps;
}

const useActionApi = <P = any, T = any>(
  api: (body?: P, cancelToken?: any) => Promise<AxiosResponse<T>>
) => {
  const [cancelToken, setCancelToken] = useState(axios.CancelToken.source());

  useEffect(() => {
    return () => cancelToken.cancel();
  }, [cancelToken]);

  const action = useCallback(
    async ({ body, loading }: ActionApiParams<P>): Promise<AxiosResponse<T>> => {
      let newCancelToken = axios.CancelToken.source();
      setCancelToken(newCancelToken);
      
      try {
        const response = await api(body, newCancelToken.token);
        return response;
      } catch (e) {
        console.error(e);
        throw e;
      }
    },
    [api]
  );

  return action;
};

export default useActionApi;
