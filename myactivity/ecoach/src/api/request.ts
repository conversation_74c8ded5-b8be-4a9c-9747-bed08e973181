import axios from "axios";
import { store } from "../store/index";
import { setToken } from "../store/ecoachSlice";

// Create axios instance
const request = axios.create({
  baseURL: "/", // Use relative path, Vite proxy will handle routing
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
request.interceptors.request.use(
  async (config) => {
    // Add cache control
    config.headers["cache-control"] = "no-cache";

    // Handle ecoach API authentication
    if (config.url?.startsWith("/ecoach-api")) {
      let cubeToken = store.getState().ecoach.cubeToken;

      // Add authorization header if token exists
      if (cubeToken) {
        config.headers["Authorization"] = `Bearer ${cubeToken}`;
      }

      config.headers["country"] = "vn";

      // Remove the /ecoach-api prefix since baseURL already points to the trainer API
      config.url = config.url.replace("/ecoach-api", "");
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
request.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.error("Unauthorized access");
    }

    return Promise.reject(error);
  }
);

export default request;
