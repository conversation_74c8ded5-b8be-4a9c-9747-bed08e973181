import axios from "axios";
import { store } from "@store/index";
import { setToken } from "@store/ecoachSlice";

// Create axios instance
const request = axios.create({
  baseURL: process.env.VITE_API_BASE_URL || "https://api.example.com",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
request.interceptors.request.use(
  async (config) => {
    // Add cache control
    config.headers["cache-control"] = "no-cache";

    // Handle ecoach API authentication
    if (config.url?.startsWith("/ecoach-api")) {
      let cubeToken = store.getState().ecoach.cubeToken;

      // If no token, get one (you might need to implement this based on your auth flow)
      if (!cubeToken) {
        // For now, we'll just use a placeholder
        // In a real app, you'd implement the token exchange here
        cubeToken = "placeholder-token";
        store.dispatch(setToken(cubeToken));
      }

      config.headers["Authorization"] = `Bearer ${cubeToken}`;
      config.headers["country"] = "vn";
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
request.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.error("Unauthorized access");
    }
    
    return Promise.reject(error);
  }
);

export default request;
