import request from "./request";
import {
  ConversationData,
  ConversationFeedbackPayload,
  ConversationHistoryResponse,
  ConversationType,
  EcoachConfigItem,
  MaintenanceStatus,
} from "@types/ecoach";

export const VERIFY_CUBE_TOKEN_URL = "auth/verify_vn_cookie";

export const getVerifyCubeToken = () => {
  return request.get<{ jwt_token: string }>(`/ecoach-api/${VERIFY_CUBE_TOKEN_URL}`, {
    headers: { country: "vn" },
  });
};

export const getConfigurationData = () => {
  return request.get<EcoachConfigItem[]>("/ecoach-api/configuration/list", {
    headers: { country: "vn" },
  });
};

export const checkMaintenanceStatus = () => {
  return request.get<MaintenanceStatus>("/ecoach-api/maintenance/list", {
    headers: { country: "vn" },
  });
};

export const checkAvatarAvailability = () => {
  return request.get<{ voice_call_available: boolean }>("/ecoach-api/conversation/available", {
    headers: { country: "vn" },
  });
};

export type ReportHistoryInput = {
  conversationType?: ConversationType;
  limit?: number;
  pageParam?: string;
};

export const getConversationHistoryByPaging = (body: ReportHistoryInput) => {
  const { limit = 100, conversationType = ConversationType.CUSTOMER, pageParam = "" } = body;
  return request.get<ConversationHistoryResponse>(
    `/ecoach-api/conversation/report_history_v2?limit=${limit}&conversation_type=${conversationType}${
      pageParam ? `&last_evaluated_key=${pageParam || ""}` : ""
    }`,
    {
      headers: { country: "vn" },
    }
  );
};

export const getConversationData = ({ conversationId }: { conversationId: string }) => {
  return request.get<ConversationData>(`/ecoach-api/conversation/report/${conversationId}`, {
    headers: { country: "vn" },
  });
};

export const submitConversationFeedback = (body: ConversationFeedbackPayload) => {
  return request.post(
    "/ecoach-api/conversation/feedback",
    {
      ...body,
    },
    {
      headers: { country: "vn" },
    }
  );
};
