import React from "react";
import styled from "styled-components";
import { useNavigate, useParams } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary, ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  min-height: 100vh;
  background: ${colors.fwdGrey[20]};
  padding: ${sizes[4]}px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${sizes[6]}px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
`;

const Title = styled(H3)`
  color: ${colors.fwdDarkGreen[100]};
`;

const BackButton = styled(ButtonSecondary)``;

const ReportContent = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: ${sizes[6]}px;

  @media ${device.noMobile} {
    grid-template-columns: 1fr 1fr;
  }
`;

const ScoreCard = styled.div`
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[6]}px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ScoreHeader = styled.div`
  text-align: center;
  margin-bottom: ${sizes[6]}px;
`;

const OverallScore = styled.div`
  font-size: 48px;
  font-weight: 700;
  color: ${colors.fwdOrange[100]};
  margin-bottom: ${sizes[2]}px;
`;

const ScoreLabel = styled.div`
  color: ${colors.fwdGreyDarker};
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 600;
`;

const SkillScores = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
`;

const SkillItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${sizes[3]}px 0;
  border-bottom: 1px solid ${colors.fwdGrey[50]};

  &:last-child {
    border-bottom: none;
  }
`;

const SkillName = styled.div`
  color: ${colors.fwdDarkGreen[100]};
  font-weight: 500;
`;

const SkillScore = styled.div<{ score: number }>`
  font-weight: 600;
  color: ${({ score }) => 
    score >= 80 ? colors.alertGreen : 
    score >= 60 ? colors.fwdYellow[100] : 
    colors.alertRed};
`;

const FeedbackCard = styled.div`
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[6]}px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const FeedbackSection = styled.div`
  margin-bottom: ${sizes[6]}px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const FeedbackTitle = styled(H6)`
  color: ${colors.fwdDarkGreen[100]};
  margin-bottom: ${sizes[3]}px;
`;

const FeedbackText = styled.p`
  color: ${colors.fwdGreyDarkest};
  line-height: 1.6;
  font-size: 14px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${sizes[4]}px;
  justify-content: center;
  margin-top: ${sizes[8]}px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;

  @media ${device.mobile} {
    flex-direction: column;
  }
`;

const ReportDetails: React.FC = () => {
  const navigate = useNavigate();
  const { conversationId } = useParams<{ conversationId: string }>();

  const handleBack = () => {
    navigate("/session-history");
  };

  const handleNewSession = () => {
    navigate("/");
  };

  // Mock report data
  const mockReport = {
    overallScore: 85,
    skills: [
      { name: "Applied Product Knowledge", score: 88 },
      { name: "Customer Discovery", score: 82 },
      { name: "Customer Relationship", score: 90 },
      { name: "Objection Handling & Closing", score: 78 },
      { name: "Communication Skills", score: 86 },
    ],
    wellDone: "Great job on building rapport with the customer and demonstrating strong product knowledge. Your communication was clear and professional throughout the conversation.",
    improvement: "Focus on handling objections more confidently. Practice addressing price concerns with value-based responses and work on closing techniques.",
  };

  return (
    <Container>
      <Header>
        <Title>Session Report</Title>
        <BackButton onClick={handleBack}>← Back to History</BackButton>
      </Header>

      <ReportContent>
        <ScoreCard>
          <ScoreHeader>
            <OverallScore>{mockReport.overallScore}%</OverallScore>
            <ScoreLabel>Overall Score</ScoreLabel>
          </ScoreHeader>
          
          <SkillScores>
            {mockReport.skills.map((skill, index) => (
              <SkillItem key={index}>
                <SkillName>{skill.name}</SkillName>
                <SkillScore score={skill.score}>{skill.score}%</SkillScore>
              </SkillItem>
            ))}
          </SkillScores>
        </ScoreCard>

        <FeedbackCard>
          <FeedbackSection>
            <FeedbackTitle>What You Did Well</FeedbackTitle>
            <FeedbackText>{mockReport.wellDone}</FeedbackText>
          </FeedbackSection>
          
          <FeedbackSection>
            <FeedbackTitle>Areas for Improvement</FeedbackTitle>
            <FeedbackText>{mockReport.improvement}</FeedbackText>
          </FeedbackSection>
        </FeedbackCard>
      </ReportContent>

      <ActionButtons>
        <ButtonPrimary onClick={handleNewSession}>
          Start New Session
        </ButtonPrimary>
        <ButtonSecondary onClick={handleBack}>
          View All Sessions
        </ButtonSecondary>
      </ActionButtons>
    </Container>
  );
};

export default ReportDetails;
