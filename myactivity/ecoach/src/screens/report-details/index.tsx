import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import { Container, Main, MainContent, MainContentInner } from "./styled";
import ModalFullPage from "@components/modal-full-page";
import ReportDetailsPage from "../../components/report/ReportDetails";
import LoadingSection from "@components/loading";
import { useGetConversationData } from "@hooks/useGetConversationData";

const ReportDetailsScreen: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get("conversationId");
  const skillTitle = decodeURIComponent(searchParams.get("skill") || "");

  const { data: reportInfo, isLoading, getConversationReportLoading } = useGetConversationData(conversationId);

  const [skillSetData, setSkillSet] = useState<any>();

  useEffect(() => {
    if (reportInfo) {
      const skillSet = (reportInfo.report?.skill_set_details as any)?.[skillTitle];
      if (skillSet) {
        setSkillSet(skillSet);
      }
    }
  }, [skillTitle, reportInfo]);

  if (isLoading || Boolean(getConversationReportLoading) || !skillSetData) {
    return (
      <ModalFullPage show={true} title="Báo cáo chi tiết" onClose={() => navigate(-1)}>
        <Container>
          <div style={{ paddingTop: "10%" }}>
            <LoadingSection loading={true} />
          </div>
        </Container>
      </ModalFullPage>
    );
  }

  return (
    <ModalFullPage show={true} title="Báo cáo chi tiết" onClose={() => navigate(-1)}>
      <Main className="bg-[#fffbf6] min-h-screen">
        <MainContent className="flex flex-col items-center max-w-screen-md lg:max-w-screen-2xl mx-auto">
          <MainContentInner className="flex flex-col items-center w-full h-full">
            <ReportDetailsPage skillSet={skillSetData} title={skillTitle} />
          </MainContentInner>
        </MainContent>
      </Main>
    </ModalFullPage>
  );
};

export { ReportDetailsScreen };
