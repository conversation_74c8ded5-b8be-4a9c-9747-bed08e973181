import styled from "styled-components";
import { colors, sizes } from "../../components/CubeBaseElement";
import { device } from "@styles/media";

export const GuideLinesStyle = {
  PageContainer: styled.div`
    width: 100%;
    height: 100vh;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    background: linear-gradient(180deg, rgba(1, 1, 1, 0.8) 5.61%, rgba(1, 1, 1, 0.6) 18.79%, rgba(1, 1, 1, 0.8) 51.74%);
    @media ${device.noMobile} {
      flex-direction: row;
      background: linear-gradient(90deg, rgba(1, 1, 1, 0.8) 33.19%, rgba(1, 1, 1, 0.8) 93.58%);
      gap: ${sizes[8]}px;
    }
  `,
  YourGoal: styled.div`
    background: ${colors.white};
    border-radius: ${sizes[4]}px;
    padding: 0 ${sizes[2]}px;
    width: 156px;
    height: ${sizes[6]}px;
    display: flex;
    align-items: center;
    justify-content: center;
  `,

  AvatarImg: styled.img`
    position: absolute;
    bottom: 0;
    height: 90%;
    object-fit: cover;
    z-index: -1;
  `,
  QFYourGoal: styled.div`
    background: ${colors.white};
    border-radius: ${sizes[4]}px;
    padding: 0 ${sizes[2]}px;
    height: ${sizes[6]}px;
    display: flex;
    align-items: center;
    justify-content: center;
  `,
  ConvinceText: styled.div`
    width: 100%;
  `,
  ConvinceAvatarView: styled.div`
    width: 100%;
    max-width: 399px;
    padding: ${sizes[5]}px 0;
    display: flex;
    flex-direction: column;
    gap: ${sizes[4]}px;
  `,
  Container: styled.div`
    padding: 0 ${sizes[4]}px;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    @media ${device.noMobile} {
      flex-direction: row;
      gap: ${sizes[8]}px;
    }
  `,
  QFContainer: styled.div`
    padding: 0 ${sizes[4]}px;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: ${sizes[6]}px;
  `,
  ProductSelectView: styled.div`
    display: flex;
    flex-direction: row;
  `,
  TimeView: styled.div`
    margin-left: ${sizes[1]}px;
    display: flex;
    flex-direction: row;
    gap: ${sizes[1]}px;
    justify-content: center;
    align-items: center;
  `,
  IntroText: styled.span`
    font-size: 20px;
    opacity: 0.8;
    color: ${colors.white};
  `,
  OneToFour: styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
  `,
  NumberRow: styled.div`
    display: flex;
    flex-direction: row;
    gap: ${sizes[4]}px;
  `,
  Circle: styled.div`
    width: ${sizes[7]}px;
    height: ${sizes[7]}px;
    background: ${colors.fwdOrange[100]};
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  `,
  HorizontalDividerView: styled.div`
    width: 100%;
    height: 22px;
    display: flex;
    justify-content: flex-start;
    padding-left: 14px;
  `,
  HorizontalDivider: styled.div`
    width: 1px;
    height: 22px;
    background: rgba(255, 255, 255, 0.5);
  `,
  YourGoalViewTablet: styled.div`
    display: flex;
    width: 100%;
    max-width: 399px;
    @media ${device.noMobile} {
      padding: ${sizes[8]}px;
      gap: ${sizes[6]}px;
      border-radius: ${sizes[1]}px;
      border: 1px solid #d3d3d3;
      flex-direction: column;
    }
  `,
};
