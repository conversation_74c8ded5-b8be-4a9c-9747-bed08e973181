import React from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  min-height: 100vh;
  background: ${colors.fwdGrey[20]};
  padding: ${sizes[4]}px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${sizes[6]}px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const Title = styled(H3)`
  color: ${colors.fwdDarkGreen[100]};
`;

const BackButton = styled(ButtonSecondary)``;

const Content = styled.div`
  max-width: 800px;
  margin: 0 auto;
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[8]}px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Section = styled.div`
  margin-bottom: ${sizes[8]}px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled(H6)`
  color: ${colors.fwdDarkGreen[100]};
  margin-bottom: ${sizes[4]}px;
`;

const SectionContent = styled.div`
  color: ${colors.fwdGreyDarkest};
  line-height: 1.6;
`;

const List = styled.ul`
  margin: ${sizes[3]}px 0;
  padding-left: ${sizes[5]}px;
`;

const ListItem = styled.li`
  margin-bottom: ${sizes[2]}px;
`;

const Guidelines: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate("/");
  };

  return (
    <Container>
      <Header>
        <Title>Practice Guidelines</Title>
        <BackButton onClick={handleBack}>← Back to Home</BackButton>
      </Header>

      <Content>
        <Section>
          <SectionTitle>Getting Started</SectionTitle>
          <SectionContent>
            <p>
              Welcome to Ecoach! This AI-powered training platform helps you practice and improve your sales skills 
              through realistic customer conversations. Here's how to make the most of your practice sessions:
            </p>
          </SectionContent>
        </Section>

        <Section>
          <SectionTitle>Session Types</SectionTitle>
          <SectionContent>
            <List>
              <ListItem>
                <strong>Quickfire Mode:</strong> Short, focused sessions (3-5 minutes) that target specific skills 
                or scenarios. Perfect for quick practice between meetings.
              </ListItem>
              <ListItem>
                <strong>Full Experience:</strong> Comprehensive sessions (10-15 minutes) that simulate complete 
                customer interactions from introduction to close.
              </ListItem>
            </List>
          </SectionContent>
        </Section>

        <Section>
          <SectionTitle>Difficulty Levels</SectionTitle>
          <SectionContent>
            <List>
              <ListItem>
                <strong>Beginner:</strong> Designed for new agents with basic scenarios, helpful prompts, 
                and simpler objections. Focus on building confidence and fundamental skills.
              </ListItem>
              <ListItem>
                <strong>Expert:</strong> Advanced scenarios with complex objections, challenging customers, 
                and sophisticated sales situations. Perfect for experienced agents looking to refine their skills.
              </ListItem>
            </List>
          </SectionContent>
        </Section>

        <Section>
          <SectionTitle>Best Practices</SectionTitle>
          <SectionContent>
            <List>
              <ListItem>Practice regularly - even 10 minutes a day can significantly improve your skills</ListItem>
              <ListItem>Review your reports carefully and focus on areas for improvement</ListItem>
              <ListItem>Try different products and difficulty levels to challenge yourself</ListItem>
              <ListItem>Use a quiet environment with good audio quality for the best experience</ListItem>
              <ListItem>Speak naturally and treat the AI customer as you would a real client</ListItem>
            </List>
          </SectionContent>
        </Section>

        <Section>
          <SectionTitle>Scoring System</SectionTitle>
          <SectionContent>
            <p>Your performance is evaluated across five key areas:</p>
            <List>
              <ListItem><strong>Applied Product Knowledge:</strong> How well you demonstrate understanding of the product</ListItem>
              <ListItem><strong>Customer Discovery:</strong> Your ability to ask questions and understand customer needs</ListItem>
              <ListItem><strong>Customer Relationship:</strong> Building rapport and trust with the customer</ListItem>
              <ListItem><strong>Objection Handling & Closing:</strong> Addressing concerns and moving toward a sale</ListItem>
              <ListItem><strong>Communication Skills:</strong> Clarity, professionalism, and overall communication effectiveness</ListItem>
            </List>
          </SectionContent>
        </Section>

        <Section>
          <SectionTitle>Technical Requirements</SectionTitle>
          <SectionContent>
            <List>
              <ListItem>Stable internet connection</ListItem>
              <ListItem>Microphone access (you'll be prompted to allow this)</ListItem>
              <ListItem>Modern web browser (Chrome, Firefox, Safari, or Edge)</ListItem>
              <ListItem>Quiet environment for best audio quality</ListItem>
            </List>
          </SectionContent>
        </Section>
      </Content>
    </Container>
  );
};

export default Guidelines;
