import React, { useMemo } from "react";
import { colors, H3, H4, H7, <PERSON>Label, SmallBody, Spacer } from "@components/CubeBaseElement";
import { avatarMobile, avatarTablet, cafeBGMobile, cafeBGTablet } from "@assets";
import useWindowResize from "@hooks/useWindowResize";
import { useAppSelector } from "@store/hooks";
import { useNavigate, useSearchParams } from "react-router-dom";
import FullScreenPage from "@components/FullScreenPage";
import { ECOACH_COUNTRY, ProductFlowType } from "@types/ecoach-custom";
import OperatingHoursIcon from "@components/icons/OperatingHoursIcon";
import { ButtonPrimary } from "@styles/buttons";
import { GuideLinesStyle } from "./styled";

const DividerHorizontal = () => (
  <GuideLinesStyle.HorizontalDividerView>
    <GuideLinesStyle.HorizontalDivider />
  </GuideLinesStyle.HorizontalDividerView>
);

const GuideLinesPage = () => {
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const productFlowType = searchParams.get("productFlowType") || "";
  const productSelectionCode = searchParams.get("productSelectionCode") || "";
  const {
    ecoach: { productConfig, quickfireProductConfig, homepageBackground },
  } = useAppSelector((state) => state);

  const productName = useMemo(() => {
    const productForCurrentCountry =
      productFlowType === ProductFlowType.QUICKFIRE
        ? quickfireProductConfig?.[ECOACH_COUNTRY]
        : productConfig?.[ECOACH_COUNTRY];

    return (
      productForCurrentCountry?.find((product) => product.product_code === productSelectionCode)?.product_name ||
      "FWD Đón đầu thay đổi 3.0"
    );
  }, [productConfig, quickfireProductConfig, productFlowType]);

  const goVideoPage = () => router.push(`/ecoach/video-call?${searchParams}`);

  const getAvatarImage = () => {
    if (noMobile) {
      return homepageBackground?.avatar_image_tablet || avatarTablet.src;
    } else {
      return homepageBackground?.avatar_image_tablet || avatarMobile.src;
    }
  };

  return (
    <FullScreenPage
      showHeader={true}
      goBack={() => router.back()}
      backgroundImage={noMobile ? cafeBGTablet.src : cafeBGMobile.src}
    >
      {<GuideLinesStyle.AvatarImg src={getAvatarImage()} alt="doc-img" />}
      <GuideLinesStyle.PageContainer>
        {productFlowType === ProductFlowType.QUICKFIRE ? (
          <GuideLinesStyle.QFContainer>
            <GuideLinesStyle.QFYourGoal>
              <SmallBody fontWeight="bold" color={"#B74701"}>
                {noMobile ? `Đây là mục tiêu của bạn:` : `2 phút | Chế độ phản xạ nhanh`}
              </SmallBody>
            </GuideLinesStyle.QFYourGoal>
            <H4 fontWeight="bold" color={colors.white} style={{ textAlign: "center" }}>
              Trả lời càng nhiều câu hỏi càng tốt để kiếm điểm!
            </H4>
            <ButtonPrimary style={{ width: 348, marginTop: noMobile ? 24 : 8 }} onClick={goVideoPage}>
              Bắt đầu trò chuyện
            </ButtonPrimary>
          </GuideLinesStyle.QFContainer>
        ) : (
          <GuideLinesStyle.Container>
            <GuideLinesStyle.ConvinceAvatarView>
              {!noMobile && (
                <GuideLinesStyle.YourGoal>
                  <SmallBody fontWeight="bold" color={"#B74701"}>
                    Đây là mục tiêu của bạn:
                  </SmallBody>
                </GuideLinesStyle.YourGoal>
              )}
              <GuideLinesStyle.ConvinceText>
                {noMobile ? (
                  <H3 fontWeight="bold" color={colors.white}>
                    Thuyết phục Minh Khang mua {productName}
                  </H3>
                ) : (
                  <H4 fontWeight="bold" color={colors.white}>
                    Thuyết phục Minh Khang mua {productName}
                  </H4>
                )}
                {noMobile && (
                  <GuideLinesStyle.ProductSelectView>
                    <LargeLabel fontWeight="500" color={colors.fwdOrange[100]}>
                      {productFlowType === ProductFlowType.QUICKFIRE ? "Chế Độ Phản Xạ Nhanh " : "Chế Độ Nhập Vai "}
                    </LargeLabel>
                    <GuideLinesStyle.TimeView>
                      <LargeLabel fontWeight="normal" color={colors.white}>
                        |
                      </LargeLabel>
                      <OperatingHoursIcon size={16} fill={colors.white} />
                      <LargeLabel fontWeight="normal" color={colors.white}>
                        {productFlowType === ProductFlowType.QUICKFIRE ? "2 phút" : "8 phút"}
                      </LargeLabel>
                    </GuideLinesStyle.TimeView>
                  </GuideLinesStyle.ProductSelectView>
                )}
              </GuideLinesStyle.ConvinceText>
              <GuideLinesStyle.IntroText>
                Bạn là một tư vấn viên gặp Minh Khang, là khách hàng trẻ mới được giới thiệu. Hít một hơi thật sâu và
                sẵn sàng nào!
              </GuideLinesStyle.IntroText>

              {noMobile && (
                <ButtonPrimary style={{ maxWidth: 180, marginTop: 32 }} onClick={goVideoPage}>
                  Bắt đầu trò chuyện
                </ButtonPrimary>
              )}
            </GuideLinesStyle.ConvinceAvatarView>
            <GuideLinesStyle.YourGoalViewTablet>
              {noMobile && (
                <GuideLinesStyle.YourGoal>
                  <SmallBody fontWeight="bold" color={"#B74701"}>
                    Đây là mục tiêu của bạn:
                  </SmallBody>
                </GuideLinesStyle.YourGoal>
              )}
              <GuideLinesStyle.OneToFour>
                {[
                  "Trò chuyện và xây dựng mối quan hệ",
                  "Hiểu rõ nhu cầu cá nhân",
                  "Trình bày các tùy chọn sản phẩm",
                  "Giải quyết quan ngại và chốt giao dịch",
                ].map((text, index) => (
                  <React.Fragment key={index}>
                    <GuideLinesStyle.NumberRow>
                      <GuideLinesStyle.Circle>
                        <H7 fontWeight="bold" color={colors.white}>
                          {index + 1}
                        </H7>
                      </GuideLinesStyle.Circle>
                      <H7 fontWeight="bold" color={colors.white}>
                        {text}
                      </H7>
                    </GuideLinesStyle.NumberRow>
                    {index < 3 && <DividerHorizontal />}
                  </React.Fragment>
                ))}

                {!noMobile && (
                  <>
                    <Spacer height={48} />
                    <ButtonPrimary maxWidth onClick={goVideoPage}>
                      Bắt đầu trò chuyện
                    </ButtonPrimary>
                    <Spacer height={48} />
                  </>
                )}
              </GuideLinesStyle.OneToFour>
            </GuideLinesStyle.YourGoalViewTablet>
          </GuideLinesStyle.Container>
        )}
      </GuideLinesStyle.PageContainer>
    </FullScreenPage>
  );
};

export default GuideLinesPage;
