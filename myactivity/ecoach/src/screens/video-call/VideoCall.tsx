import React, { useState } from "react";
import styled from "styled-components";
import { useNavigate, useSearchParams } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary, ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: ${colors.black};
  position: relative;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${sizes[4]}px;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10;
`;

const SessionInfo = styled.div`
  color: ${colors.white};
`;

const VideoArea = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, ${colors.fwdDarkGreen[100]}, ${colors.fwdDarkGreen[50]});
  position: relative;
`;

const VideoPlaceholder = styled.div`
  width: 300px;
  height: 200px;
  background: ${colors.fwdGrey[100]};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${colors.fwdGreyDarker};

  @media ${device.noMobile} {
    width: 500px;
    height: 350px;
  }
`;

const Controls = styled.div`
  display: flex;
  justify-content: center;
  gap: ${sizes[4]}px;
  padding: ${sizes[6]}px;
  background: rgba(0, 0, 0, 0.8);
`;

const StatusIndicator = styled.div<{ isActive: boolean }>`
  position: absolute;
  top: ${sizes[4]}px;
  right: ${sizes[4]}px;
  padding: ${sizes[2]}px ${sizes[4]}px;
  border-radius: 20px;
  background: ${({ isActive }) => (isActive ? colors.alertGreen : colors.alertRed)};
  color: ${colors.white};
  font-size: 12px;
  font-weight: 600;
`;

const Timer = styled.div`
  color: ${colors.white};
  font-size: 18px;
  font-weight: 600;
`;

const VideoCall: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isCallActive, setIsCallActive] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  const sessionType = searchParams.get("type") || "full";
  const product = searchParams.get("product") || "";
  const level = searchParams.get("level") || "";

  const handleStartCall = () => {
    setIsCallActive(true);
    // In a real app, this would start the actual video call
    // For demo purposes, we'll simulate a call duration
    const interval = setInterval(() => {
      setCallDuration((prev) => prev + 1);
    }, 1000);

    // Auto-end call after 30 seconds for demo
    setTimeout(() => {
      clearInterval(interval);
      handleEndCall();
    }, 30000);
  };

  const handleEndCall = () => {
    setIsCallActive(false);
    // Navigate to report with mock conversation ID
    const mockConversationId = `conv_${Date.now()}`;
    navigate(`/report/details/${mockConversationId}`);
  };

  const handleBack = () => {
    navigate(`/product-level-selection?type=${sessionType}&product=${product}`);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getProductName = (productCode: string) => {
    switch (productCode) {
      case "set_for_life":
        return "Set for Life";
      case "set_for_health":
        return "Set for Health";
      default:
        return "Product";
    }
  };

  return (
    <Container>
      <Header>
        <SessionInfo>
          <div style={{ color: colors.white, fontSize: "16px", fontWeight: "600" }}>
            {getProductName(product)} - {level.charAt(0).toUpperCase() + level.slice(1)}
          </div>
          <div style={{ color: colors.fwdGrey[50], fontSize: "14px" }}>
            {sessionType === "quickfire" ? "Quickfire Mode" : "Full Experience"}
          </div>
        </SessionInfo>
        <Timer>{formatTime(callDuration)}</Timer>
      </Header>

      <VideoArea>
        <StatusIndicator isActive={isCallActive}>
          {isCallActive ? "● LIVE" : "● READY"}
        </StatusIndicator>
        
        <VideoPlaceholder>
          {isCallActive ? "Video Call in Progress..." : "Click Start to Begin"}
        </VideoPlaceholder>
      </VideoArea>

      <Controls>
        {!isCallActive ? (
          <>
            <ButtonSecondary onClick={handleBack}>← Back</ButtonSecondary>
            <ButtonPrimary onClick={handleStartCall}>Start Call</ButtonPrimary>
          </>
        ) : (
          <ButtonSecondary onClick={handleEndCall}>End Call</ButtonSecondary>
        )}
      </Controls>
    </Container>
  );
};

export default VideoCall;
