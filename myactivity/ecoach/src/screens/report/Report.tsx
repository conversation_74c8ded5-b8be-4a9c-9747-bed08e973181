import React from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary } from "@components/Button";

const Container = styled.div`
  width: 100%;
  min-height: 100vh;
  background: ${colors.fwdGrey[20]};
  padding: ${sizes[4]}px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const ContentWrapper = styled.div`
  text-align: center;
  max-width: 600px;
`;

const Title = styled(H3)`
  color: ${colors.fwdDarkGreen[100]};
  margin-bottom: ${sizes[6]}px;
`;

const Message = styled.p`
  color: ${colors.fwdGreyDarker};
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: ${sizes[8]}px;
`;

const Report: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <Container>
      <ContentWrapper>
        <Title>Report</Title>
        <Message>
          This is a placeholder for the general report page. 
          Individual session reports can be accessed from the session history.
        </Message>
        <ButtonPrimary onClick={handleGoHome}>
          Back to Home
        </ButtonPrimary>
      </ContentWrapper>
    </Container>
  );
};

export default Report;
