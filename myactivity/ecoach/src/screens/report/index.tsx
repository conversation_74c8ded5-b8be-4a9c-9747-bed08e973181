import React, { useCallback, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ReportLoading } from "@components/report/ReportLoading";
import { Container, Main, MainContent, MainContentInner, MainContentLoading, MainLoading } from "./styled";
import ModalFullPage from "@components/modal-full-page";
import { Report2 } from "@components/report/Report2";
import LoadingSection from "@components/loading";
import ReportReady from "@components/report/ReportReady";
import { useGetConversationData } from "@hooks/useGetConversationData";
import FeedbackModal from "@components/modal/FeedbackModal";

const ReportScreen: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get("conversationId") as string;
  const isFromVideoCall = searchParams.get("ref") === "call";
  const isFromHistoryList = searchParams.get("ref") === "history";
  const [feedbackVisible, setFeedbackVisible] = useState<boolean | undefined>();

  const {
    data: reportInfo,
    loading: isLoading,
    error,
  } = useGetConversationData(conversationId);

  // TODO: Add these missing properties to the hook
  const getConversationReportLoading = false;
  const isTimeout = false;
  const showReportReady = false;
  const setShowReportReady = () => {};

  useEffect(() => {
    if (isTimeout) {
      navigate("/");
    }
  }, [isTimeout]);

  const goBack = useCallback(() => {
    if (isFromHistoryList) {
      navigate(-1);
    } else {
      navigate("/");
    }
  }, [isFromHistoryList]);

  const handleUserUpdate = () => setFeedbackVisible(true);

  if ((isFromVideoCall && isLoading) || feedbackVisible) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => navigate("/")}>
        <Container className="bg-[#fffbf6] min-h-screen">
          <MainLoading className="flex flex-col items-center max-w-screen-2xl mx-auto">
            <MainContentLoading className="bg-white flex flex-col items-center w-full">
              <ReportLoading />
              <FeedbackModal
                conversationId={conversationId}
                handleUserUpdate={handleUserUpdate}
                visible={feedbackVisible || feedbackVisible === undefined}
                setVisible={setFeedbackVisible}
                title="Bạn thấy phiên làm việc như thế nào"
                feedbackType="ROLE_PLAY"
              />
            </MainContentLoading>
          </MainLoading>
        </Container>
      </ModalFullPage>
    );
  }

  if (showReportReady) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => {/* TODO: Fix navigation */}}>
        <Container className="bg-[#fffbf6] min-h-screen">
          <MainLoading className="flex flex-col items-center max-w-screen-2xl mx-auto">
            <MainContentLoading className="bg-white flex flex-col items-center w-full">
              <ReportReady hideReadyReport={setShowReportReady} />
            </MainContentLoading>
          </MainLoading>
        </Container>
      </ModalFullPage>
    );
  }

  if (isLoading || Boolean(getConversationReportLoading)) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => goBack()}>
        <Container>
          <div style={{ paddingTop: "10%" }}>
            <LoadingSection loading={true} />
          </div>
        </Container>
      </ModalFullPage>
    );
  }

  return (
    <ModalFullPage show={true} title="Báo cáo" onClose={() => goBack()}>
      <Main className="bg-[#fffbf6] min-h-screen">
        <MainContent className="flex flex-col items-center max-w-screen-md lg:max-w-screen-2xl mx-auto">
          <MainContentInner className="flex flex-col items-center w-full h-full">
            {reportInfo && <Report2 reportDetails={reportInfo} isFromHistoryList={isFromHistoryList} />}
          </MainContentInner>
        </MainContent>
      </Main>
    </ModalFullPage>
  );
};

export { ReportScreen };
