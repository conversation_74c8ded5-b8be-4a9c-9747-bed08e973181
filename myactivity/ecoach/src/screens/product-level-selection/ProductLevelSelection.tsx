import React from "react";
import styled from "styled-components";
import { useNavigate, useSearchParams } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary, ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${colors.fwdDarkGreen[100]};
  padding: ${sizes[4]}px;
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  gap: ${sizes[6]}px;
`;

const Title = styled(H3)`
  color: ${colors.white};
  margin-bottom: ${sizes[4]}px;
`;

const Subtitle = styled(H6)`
  color: ${colors.fwdGrey[50]};
  margin-bottom: ${sizes[8]}px;
`;

const LevelGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${sizes[4]}px;
  width: 100%;
  max-width: 400px;

  @media ${device.noMobile} {
    grid-template-columns: 1fr 1fr;
    max-width: 500px;
  }
`;

const LevelCard = styled.div`
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[6]}px;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
  }
`;

const LevelTitle = styled(H6)`
  color: ${colors.fwdDarkGreen[100]};
  margin-bottom: ${sizes[2]}px;
`;

const LevelDescription = styled.p`
  color: ${colors.fwdGreyDarker};
  font-size: 14px;
  line-height: 1.5;
`;

const BackButton = styled(ButtonSecondary)`
  position: absolute;
  top: ${sizes[6]}px;
  left: ${sizes[4]}px;
`;

const ProductLevelSelection: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const sessionType = searchParams.get("type") || "full";
  const product = searchParams.get("product") || "";

  const handleLevelSelect = (level: string) => {
    navigate(`/video-call?type=${sessionType}&product=${product}&level=${level}`);
  };

  const handleBack = () => {
    navigate(`/product-selection?type=${sessionType}`);
  };

  const getProductName = (productCode: string) => {
    switch (productCode) {
      case "set_for_life":
        return "Set for Life";
      case "set_for_health":
        return "Set for Health";
      default:
        return "Product";
    }
  };

  return (
    <Container>
      <BackButton onClick={handleBack}>← Back</BackButton>
      
      <ContentWrapper>
        <Title>Select Difficulty Level</Title>
        <Subtitle>
          Choose your difficulty level for {getProductName(product)}
          {sessionType === "quickfire" ? " (Quickfire Mode)" : " (Full Experience)"}
        </Subtitle>
        
        <LevelGrid>
          <LevelCard onClick={() => handleLevelSelect("beginner")}>
            <LevelTitle>Beginner</LevelTitle>
            <LevelDescription>
              Perfect for new agents. Basic scenarios with helpful guidance and simpler objections.
            </LevelDescription>
          </LevelCard>
          
          <LevelCard onClick={() => handleLevelSelect("expert")}>
            <LevelTitle>Expert</LevelTitle>
            <LevelDescription>
              For experienced agents. Complex scenarios with challenging objections and advanced techniques.
            </LevelDescription>
          </LevelCard>
        </LevelGrid>
      </ContentWrapper>
    </Container>
  );
};

export default ProductLevelSelection;
