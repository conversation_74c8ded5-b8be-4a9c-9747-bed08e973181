import React from "react";
import styled from "styled-components";
import { useNavigate, useSearchParams } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary, ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: ${colors.fwdDarkGreen[100]};
  padding: ${sizes[4]}px;
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  gap: ${sizes[6]}px;
`;

const Title = styled(H3)`
  color: ${colors.white};
  margin-bottom: ${sizes[4]}px;
`;

const Subtitle = styled(H6)`
  color: ${colors.fwdGrey[50]};
  margin-bottom: ${sizes[8]}px;
`;

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${sizes[4]}px;
  width: 100%;
  max-width: 400px;

  @media ${device.noMobile} {
    grid-template-columns: 1fr 1fr;
    max-width: 500px;
  }
`;

const ProductCard = styled.div`
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[6]}px;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
  }
`;

const ProductTitle = styled(H6)`
  color: ${colors.fwdDarkGreen[100]};
  margin-bottom: ${sizes[2]}px;
`;

const ProductDescription = styled.p`
  color: ${colors.fwdGreyDarker};
  font-size: 14px;
  line-height: 1.5;
`;

const BackButton = styled(ButtonSecondary)`
  position: absolute;
  top: ${sizes[6]}px;
  left: ${sizes[4]}px;
`;

const ProductSelection: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const sessionType = searchParams.get("type") || "full";

  const handleProductSelect = (productCode: string) => {
    navigate(`/product-level-selection?type=${sessionType}&product=${productCode}`);
  };

  const handleBack = () => {
    navigate("/");
  };

  return (
    <Container>
      <BackButton onClick={handleBack}>← Back</BackButton>
      
      <ContentWrapper>
        <Title>Select Product</Title>
        <Subtitle>
          Choose the product you want to practice selling
          {sessionType === "quickfire" ? " (Quickfire Mode)" : " (Full Experience)"}
        </Subtitle>
        
        <ProductGrid>
          <ProductCard onClick={() => handleProductSelect("set_for_life")}>
            <ProductTitle>Set for Life</ProductTitle>
            <ProductDescription>
              Life insurance product designed to provide comprehensive coverage and financial security.
            </ProductDescription>
          </ProductCard>
          
          <ProductCard onClick={() => handleProductSelect("set_for_health")}>
            <ProductTitle>Set for Health</ProductTitle>
            <ProductDescription>
              Health insurance product offering medical coverage and wellness benefits.
            </ProductDescription>
          </ProductCard>
        </ProductGrid>
      </ContentWrapper>
    </Container>
  );
};

export default ProductSelection;
