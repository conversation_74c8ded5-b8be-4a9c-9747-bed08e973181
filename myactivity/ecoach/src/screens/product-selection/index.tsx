import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  ButtonContainer,
  H6,
  PageContainer,
  ProductCardContainer,
  ProductContainer,
  ProductImage,
  ProductImageContainer,
  SmallLabel,
  Spacer,
  Title,
  TitleContainer,
} from "./styled";
import { ButtonPrimary } from "styles/buttons";
import { useAppSelector } from "hooks/use-redux";
import { useSearchParams } from "react-router-dom";
import LoadingSection from "components/loading";
import { ECOACH_COUNTRY, EcoachProductConfig, ProductFlowType } from "../../@custom-types/ecoach";
import FullScreenPage from "../../components/FullScreenPage";

const ProductSelectionScreen = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const productFlowType = searchParams.get("productFlowType") || "";
  const [selectedProductCode, setSelectedProductCode] = useState<string | null>(null);

  const {
    rootReducer: {
      loading: { getEcoachConfigurationDataLoading },
      user: { channel: userChannel },
    },
    ecoachReducer: { productConfig, quickfireProductConfig },
  } = useAppSelector((state) => state);

  const availableProducts = useMemo(() => {
    const productForCurrentCountry =
      productFlowType === ProductFlowType.QUICKFIRE
        ? quickfireProductConfig?.[ECOACH_COUNTRY]
        : productConfig?.[ECOACH_COUNTRY];

    if (productForCurrentCountry) {
      // Filter products based on user channel
      const filteredProducts = productForCurrentCountry.filter((product) => {
        // If product doesn't have a channel field, show it to all users
        if (!product.channel) {
          return true;
        }
        // If product has a channel field, only show it if it matches the user's channel (case-insensitive)
        return product.channel.toLowerCase() === userChannel?.toLowerCase();
      });

      return [...filteredProducts].sort((a, b) => a.order - b.order);
    }

    return [];
  }, [productConfig, quickfireProductConfig, productFlowType, userChannel]);

  const handleNext = useCallback(() => {
    if (!selectedProductCode) {
      return;
    }
    navigate(
      `/ecoach/guide-lines?productFlowType=${productFlowType}&productSelectionCode=${selectedProductCode}&difficultType=Beginner`
    );
  }, [selectedProductCode, productFlowType]);

  useEffect(() => {
    const isSingleProduct = availableProducts.filter((e) => e.clickable);
    if (isSingleProduct.length === 1) {
      setSelectedProductCode(isSingleProduct[0].product_code);
    }
  }, [availableProducts]);

  return (
    <FullScreenPage showHeader={true} title="" goBack={() => navigate(-1)}>
      <PageContainer>
        <Title>Xin mời chọn sản phẩm</Title>

        {Boolean(getEcoachConfigurationDataLoading) ? (
          <div className="mt-16">
            <LoadingSection loading={true} />
          </div>
        ) : (
          <>
            <ProductContainer columns="repeat(auto-fit, minmax(290px, 400px))" gap={16} mbColumns={1}>
              {availableProducts.map((product) => (
                <ProductCard
                  key={product.product_code}
                  product={product}
                  isSelected={selectedProductCode === product.product_code}
                  onSelect={setSelectedProductCode}
                />
              ))}
            </ProductContainer>

            <ButtonContainer>
              <ButtonPrimary
                style={{
                  maxWidth: "15rem",
                  width: "100%",
                }}
                disabled={!selectedProductCode}
                onClick={handleNext}
              >
                Tiếp tục
              </ButtonPrimary>
            </ButtonContainer>
          </>
        )}
      </PageContainer>
    </FullScreenPage>
  );
};

interface ProductCardProps {
  product: EcoachProductConfig;
  isSelected: boolean;
  onSelect: (productCode: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, isSelected, onSelect }) => {
  const { product_name, product_code, product_description, product_image, product_image_base64, clickable } = product;

  return (
    <ProductCardContainer
      isSelected={isSelected}
      disabled={!clickable}
      onClick={() => clickable && onSelect?.(product_code)}
    >
      <ProductImageContainer>
        <ProductImage src={product_image || product_image_base64 || ""} />
      </ProductImageContainer>
      <Spacer height={16} />
      <TitleContainer>
        <H6 isSelected={isSelected}>{product_name}</H6>
        <SmallLabel>{product_description}</SmallLabel>
      </TitleContainer>
    </ProductCardContainer>
  );
};

export { ProductSelectionScreen };
