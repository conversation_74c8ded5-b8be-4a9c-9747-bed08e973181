import React, { useEffect, useRef } from "react";
import useWindowResize from "@hooks/use-window-resize";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { colors, H6, sizes } from "../../components/CubeBaseElement";
import { homePageBg } from "../../assets";
import LoadingSection from "@components/loading";
import { ConversationData } from "../../@custom-types/ecoach";
import ArrowLeftIcon from "../../components/icons/ArrowLeftIcon";
import { breakpoints, device } from "@styles/media";
import { useReportHistoryLatest } from "../../hooks/useReportHistoryLatest";
import FullScreenPage from "../../components/FullScreenPage";
import SessionCard from "../../components/cards/SessionCard";

const PageContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-bottom: ${sizes[10]}px;
  width: 100%;
  padding: 0 ${sizes[4]}px;
  overflow-y: auto;
  margin-top: ${sizes[4]}px;
`;

const SessionContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: ${breakpoints.lg}px;
  margin-top: ${sizes[4]}px;
  align-self: center;
  align-items: center;
`;
const SessionView = styled.div`
  gap: ${sizes[4]}px;
  width: 100%;
`;

const Content = styled.div`
  display: flex;
  gap: ${sizes[4]}px;
  flex-direction: column;
  @media ${device.noMobile} {
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: ${sizes[4]}px;
  }
`;

const BackBtn = styled.div`
  cursor: pointer;
  margin-bottom: ${sizes[4]}px;
`;

const SessionHistory = () => {
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const { data: sessionList, loading, hasMore, fetchNextPage } = useReportHistoryLatest(20);
  const containerRef = useRef<HTMLDivElement>(null);

  const goBack = () => {
    navigate(-1);
  };

  const numColumns = noMobile ? 2 : 1;
  const rows: ConversationData[][] = noMobile
    ? sessionList?.reduce((acc: ConversationData[][], item, index) => {
        if (index % numColumns === 0) acc.push([]);
        acc[acc.length - 1].push(item);
        return acc;
      }, []) || []
    : sessionList ? [sessionList] : [];

  const handleScroll = () => {
    if (!containerRef.current || loading || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    if (scrollTop + clientHeight >= scrollHeight - 50) {
      fetchNextPage && sessionList && fetchNextPage(sessionList.length + 20);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [sessionList, loading, hasMore]);

  return (
    <FullScreenPage backgroundImage={homePageBg}>
      <PageContainer ref={containerRef}>
        <BackBtn onClick={goBack}>
          <ArrowLeftIcon fill={colors.fwdDarkGreen[100]} width={30} />
        </BackBtn>
        <H6 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
          Lịch sử cuộc gọi
        </H6>
        <SessionContainer>
          {rows.length === 0 && loading && <LoadingSection loading={true} />}
          {rows.map((row, rowIndex) => (
            <SessionView key={`row-${rowIndex}`}>
              <Content>
                {row.map((item, index) => (
                  <SessionCard
                    session={item}
                    sessionNumber={item.session_number}
                    key={`${item.conversation_id}-${index}`}
                  />
                ))}
              </Content>
            </SessionView>
          ))}
          {loading && hasMore && <LoadingSection loading={true} />}
        </SessionContainer>
      </PageContainer>
    </FullScreenPage>
  );
};

export default SessionHistory;
