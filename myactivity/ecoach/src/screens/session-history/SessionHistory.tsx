import React from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonSecondary } from "@components/Button";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  min-height: 100vh;
  background: ${colors.fwdGrey[20]};
  padding: ${sizes[4]}px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${sizes[6]}px;
`;

const Title = styled(H3)`
  color: ${colors.fwdDarkGreen[100]};
`;

const BackButton = styled(ButtonSecondary)``;

const SessionList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  max-width: 800px;
  margin: 0 auto;
`;

const SessionCard = styled.div`
  background: ${colors.white};
  border-radius: 12px;
  padding: ${sizes[6]}px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
  }
`;

const SessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${sizes[3]}px;
`;

const SessionTitle = styled(H6)`
  color: ${colors.fwdDarkGreen[100]};
`;

const SessionDate = styled.div`
  color: ${colors.fwdGreyDarker};
  font-size: 14px;
`;

const SessionDetails = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${sizes[4]}px;
  margin-bottom: ${sizes[3]}px;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[1]}px;
`;

const DetailLabel = styled.div`
  color: ${colors.fwdGreyDarker};
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
`;

const DetailValue = styled.div`
  color: ${colors.fwdDarkGreen[100]};
  font-size: 14px;
  font-weight: 500;
`;

const ScoreIndicator = styled.div<{ score: number }>`
  padding: ${sizes[2]}px ${sizes[3]}px;
  border-radius: 20px;
  background: ${({ score }) => 
    score >= 80 ? colors.alertGreen : 
    score >= 60 ? colors.fwdYellow[100] : 
    colors.alertRed};
  color: ${colors.white};
  font-size: 12px;
  font-weight: 600;
  text-align: center;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${sizes[12]}px;
  color: ${colors.fwdGreyDarker};
`;

const SessionHistory: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate("/");
  };

  const handleSessionClick = (conversationId: string) => {
    navigate(`/report/details/${conversationId}`);
  };

  // Mock session data
  const mockSessions = [
    {
      id: "conv_1",
      product: "Set for Life",
      level: "Beginner",
      type: "Full Experience",
      date: "2024-01-15",
      duration: "8:45",
      score: 85,
    },
    {
      id: "conv_2",
      product: "Set for Health",
      level: "Expert",
      type: "Quickfire",
      date: "2024-01-14",
      duration: "5:30",
      score: 72,
    },
    {
      id: "conv_3",
      product: "Set for Life",
      level: "Expert",
      type: "Full Experience",
      date: "2024-01-13",
      duration: "12:15",
      score: 91,
    },
  ];

  return (
    <Container>
      <Header>
        <Title>Session History</Title>
        <BackButton onClick={handleBack}>← Back to Home</BackButton>
      </Header>

      <SessionList>
        {mockSessions.length === 0 ? (
          <EmptyState>
            <H6 style={{ color: colors.fwdGreyDarker, marginBottom: sizes[4] }}>
              No sessions yet
            </H6>
            <p>Start your first practice session to see your history here.</p>
          </EmptyState>
        ) : (
          mockSessions.map((session) => (
            <SessionCard key={session.id} onClick={() => handleSessionClick(session.id)}>
              <SessionHeader>
                <SessionTitle>{session.product}</SessionTitle>
                <SessionDate>{session.date}</SessionDate>
              </SessionHeader>
              
              <SessionDetails>
                <DetailItem>
                  <DetailLabel>Level</DetailLabel>
                  <DetailValue>{session.level}</DetailValue>
                </DetailItem>
                
                <DetailItem>
                  <DetailLabel>Type</DetailLabel>
                  <DetailValue>{session.type}</DetailValue>
                </DetailItem>
                
                <DetailItem>
                  <DetailLabel>Duration</DetailLabel>
                  <DetailValue>{session.duration}</DetailValue>
                </DetailItem>
                
                <DetailItem>
                  <DetailLabel>Score</DetailLabel>
                  <ScoreIndicator score={session.score}>{session.score}%</ScoreIndicator>
                </DetailItem>
              </SessionDetails>
            </SessionCard>
          ))
        )}
      </SessionList>
    </Container>
  );
};

export default SessionHistory;
