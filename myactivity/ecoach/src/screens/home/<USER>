import React, { useEffect } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, H6, colors, sizes } from "@components/CubeBaseElement";
import { ButtonPrimary } from "@components/Button";
import { useAppSelector, useAppDispatch } from "@store/index";
import { setUser } from "@store/appSlice";
import { device } from "@styles/media";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );
  padding: ${sizes[4]}px;

  @media ${device.noMobile} {
    background: linear-gradient(
      0deg,
      #000 47%,
      rgba(0, 0, 0, 0) 65.55%,
      rgba(0, 0, 0, 0) 88.41%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  gap: ${sizes[6]}px;
`;

const Title = styled(H3)`
  color: ${colors.white};
  margin-bottom: ${sizes[4]}px;
`;

const Subtitle = styled(H6)`
  color: ${colors.fwdGrey[50]};
  margin-bottom: ${sizes[8]}px;
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  width: 100%;
  max-width: 300px;

  @media ${device.noMobile} {
    flex-direction: row;
    max-width: 500px;
  }
`;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.app);

  useEffect(() => {
    // Initialize user data if needed
    if (!user.name) {
      dispatch(setUser({
        channel: "AGENCY",
        email: "<EMAIL>",
        name: "Demo User",
        username: "demo",
        designation: "Agent",
        designationDesc: "Sales Agent",
      }));
    }
  }, [dispatch, user.name]);

  const handleStartQuickfire = () => {
    navigate("/product-selection?type=quickfire");
  };

  const handleStartFullExperience = () => {
    navigate("/product-selection?type=full");
  };

  const handleViewHistory = () => {
    navigate("/session-history");
  };

  const handleViewGuidelines = () => {
    navigate("/guidelines");
  };

  return (
    <Container>
      <ContentWrapper>
        <Title>Welcome to Ecoach</Title>
        <Subtitle>
          Practice your sales skills with AI-powered conversations
          {user.name && ` Hello, ${user.name}!`}
        </Subtitle>
        
        <ButtonGroup>
          <ButtonPrimary size="large" fullWidth onClick={handleStartQuickfire}>
            Start Quickfire Session
          </ButtonPrimary>
          <ButtonPrimary size="large" fullWidth onClick={handleStartFullExperience}>
            Start Full Experience
          </ButtonPrimary>
          <ButtonPrimary size="medium" fullWidth onClick={handleViewHistory}>
            View Session History
          </ButtonPrimary>
          <ButtonPrimary size="medium" fullWidth onClick={handleViewGuidelines}>
            View Guidelines
          </ButtonPrimary>
        </ButtonGroup>
      </ContentWrapper>
    </Container>
  );
};

export default HomePage;
