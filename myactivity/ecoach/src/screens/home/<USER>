import React, { useMemo, useRef, useState } from "react";
import styled from "styled-components";
import { colors, H6, H7, H8, sizes, Spacer } from "../../components/CubeBaseElement";
import { avatarMobile, avatarTablet, cafeBGMobile, cafeBGTablet } from "../../assets";
import NavigationCard, { NavigationCardType } from "../../components/cards/NavigationCard";
import SessionQuickFireCard from "../../components/cards/SessionQuickFireCard";
import { ConversationData } from "../../@custom-types/ecoach";
import useWindowResize from "@hooks/use-window-resize";
import { useReportHistoryLatest } from "../../hooks/useReportHistoryLatest";
import { useAppSelector } from "@store/hooks";
import { breakpoints, device } from "@styles/media";
import CloseIcon from "../../assets/icons/CloseIcon";
import LoadingSection from "@components/loading";
import { useNavigate } from "react-router-dom";
import FullScreenPage from "../../components/FullScreenPage";
import useMaintenance from "../../hooks/useMaintenance";
import MaintenancePage from "../../components/Maintenance";
import GoDownIcon from "../../assets/icons/GoDownIcon";
import { Rate } from "../../components/icons/Rate";
import FeedbackModal from "../../components/modal/FeedbackModal";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );

  @media ${device.noMobile} {
    background: linear-gradient(
      0deg,
      #000 47%,
      rgba(0, 0, 0, 0) 65.55%,
      rgba(0, 0, 0, 0) 88.41%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
`;

const AvatarImg = styled.img`
  position: absolute;
  top: 50px;
  width: 100%;
  z-index: -1;
  @media ${device.noMobile} {
    object-fit: cover;
    height: 550px;
    right: 25%;
    width: 50%;
  }
`;

const HorizontalView = styled.div`
  display: flex;
  width: 100%;
  max-width: ${breakpoints.sm}px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
  gap: ${sizes[4]}px;
`;

const Header = styled.div`
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  margin-top: ${sizes[7]}px;
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
`;

const HeaderRightView = styled.div`
  display: flex;
  gap: ${sizes[4]}px;
  flex-direction: row;
  justify-content: space-between;
`;

const HiView = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 202px;
  margin-bottom: 32px;
  @media ${device.noMobile} {
    margin-top: 285px;
    margin-bottom: 52px;
  }
`;

const HiText = styled(H6)`
  text-align: center;
  margin-left: ${sizes[9]}px;
  margin-right: ${sizes[9]}px;
`;

const DesText = styled(H8)`
  text-align: center;
  align-self: center;
  padding-top: ${sizes[2]}px;
  margin: 0 ${sizes[18]}px;
`;

const WatchVideo = styled.div`
  display: flex;
  margin-top: ${sizes[4]}px;
  width: 100%;
  align-items: center;
  justify-content: center;
  background-color: transparent;
`;

const DividerView = styled.div`
  margin-top: ${sizes[10]}px;
  margin-bottom: ${sizes[6]}px;
  width: 100%;
  height: 1px;
  background: #8b9793;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
`;
const GoDownView = styled.div`
  left: calc(50% - 16px);
  width: 32px;
  height: 32px;
  background: ${colors.fwdDarkGreen[50]};
  border-radius: 50%;
  padding: 4px;
  cursor: pointer;
`;

const SessionView = styled.div`
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
  gap: ${sizes[4]}px;
  padding-bottom: ${sizes[4]}px;
  margin-bottom: ${sizes[10]}px;
  width: 100%;
  max-width: ${breakpoints.sm}px;
`;
const Content = styled.div`
  @media ${device.noMobile} {
    display: flex;
    gap: ${sizes[4]}px;
    justify-content: space-between;
    margin-bottom: ${sizes[4]}px;
  }
`;

const Btn = styled.div`
  cursor: pointer;
  display: flex;
  justify-content: center;
`;

const TGHomePage = () => {
  const { width } = useWindowResize();
  const noMobile = width > 768;
  const navigate = useNavigate();
  const bottomRef = useRef<HTMLDivElement>(null);
  const {
    ecoach: { homepageBackground, quickfireVideoUrl },
    app: {
      user: { name },
    },
  } = useAppSelector((state) => state);
  // console.log('TGHomePage homepageBackground', homepageBackground)
  const validUrl = quickfireVideoUrl && quickfireVideoUrl.length > 0 && quickfireVideoUrl.includes("https://");
  const { data, loading } = useReportHistoryLatest(5);
  const { isMaintenanceInProgress } = useMaintenance();
  const [feedbackVisible, setFeedbackVisible] = useState(false);

  const sessionList = useMemo(() => {
    if (!data) {
      return [];
    }
    return data.slice(0, noMobile ? 4 : 3);
  }, [data, noMobile]);

  const hasMoreItems = useMemo(() => {
    if (!data) {
      return false;
    }
    return data.length > (noMobile ? 4 : 3);
  }, [data, noMobile]);

  const goToQuickFireCall = () => {
    navigate("/ecoach/product-selection?productFlowType=quickfire");
  };

  const goToSelectPolicy = () => {
    navigate("/ecoach/product-selection");
  };

  const goToWatchVideoPage = () => {
    if (validUrl) {
      window.open(quickfireVideoUrl, "_blank");
    }
  };
  const goToSSHistory = () => {
    navigate("/ecoach/session-history");
  };

  const closeBtn = () => {
    navigate("/trang-chu", { replace: true });
  };

  const openFeedbackModal = () => {
    setFeedbackVisible(true);
  };

  const goBottomBtn = () => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  const numColumns = noMobile ? 2 : 1;

  const rows: ConversationData[][] = noMobile
    ? sessionList.reduce((acc: ConversationData[][], item, index) => {
        if (index % numColumns === 0) acc.push([]);
        acc[acc.length - 1].push(item);
        return acc;
      }, [])
    : [sessionList];

  const getBGImage = () => {
    if (noMobile) {
      return homepageBackground?.background_image_tablet || cafeBGTablet;
    } else {
      return homepageBackground?.background_image_mobile || cafeBGMobile;
    }
  };
  const getAvatarImage = () => {
    if (noMobile) {
      return homepageBackground?.avatar_image_tablet || avatarTablet;
    } else {
      return homepageBackground?.avatar_image_tablet || avatarMobile;
    }
  };

  if (isMaintenanceInProgress) {
    return <MaintenancePage />;
  }

  return (
    <FullScreenPage backgroundImage={getBGImage()}>
      {<AvatarImg src={getAvatarImage()} alt="doc-img" />}
      <>
        {feedbackVisible && (
          <FeedbackModal
            visible={feedbackVisible}
            setVisible={setFeedbackVisible}
            title="Đánh giá chung"
            feedbackType="OVERALL"
          />
        )}
      </>
      <Container>
        <Header>
          <H6 fontWeight="bold" color={"#FEF9F4"}>
            Chuyên Gia{" "}
            <H6 fontWeight="bold" color={"#E87722"}>
              Đào Tạo
            </H6>
          </H6>
          <HeaderRightView>
            <Btn onClick={openFeedbackModal}>
              <Rate fill={colors.white} width={30} height={30} />
            </Btn>
            <Btn onClick={closeBtn}>
              <CloseIcon fill={colors.white} size={30} />
            </Btn>
          </HeaderRightView>
        </Header>
        <HiView>
          <HiText fontWeight={"bold"} color={"white"}>
            Chào {name}
          </HiText>
          <HiText fontWeight={"bold"} color={"white"}>
            Hãy sẵn sàng để bán hàng
          </HiText>
          <DesText fontWeight={"normal"} color={"white"}>
            Rèn luyện kỹ năng bán hàng của bạn và thuyết phục khách hàng AI mua sản phẩm của FWD.
          </DesText>
        </HiView>
        <HorizontalView>
          <NavigationCard navigationCardType={NavigationCardType.SalesRolePlay} onPress={goToSelectPolicy} />
          <NavigationCard navigationCardType={NavigationCardType.Quickfire} onPress={goToQuickFireCall} />
        </HorizontalView>

        {validUrl && (
          <WatchVideo onClick={goToWatchVideoPage}>
            <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
              Hãy xem thử video nhập vai
            </H8>
          </WatchVideo>
        )}

        <DividerView>
          <GoDownView onClick={goBottomBtn}>
            <GoDownIcon fill={colors.fwdDarkGreen[20]} secondFill={colors.fwdGreyDark[100]} size={24}></GoDownIcon>
          </GoDownView>
        </DividerView>

        {loading && <LoadingSection loading={true} />}

        {sessionList && sessionList.length > 0 && (
          <SessionView>
            <Spacer height={sizes[4]} />
            <H6 fontWeight="bold" color={colors.white}>
              {noMobile ? "Báo Cáo Chế Độ Nhập Vai" : "Báo Cáo Trải Nghiệm Toàn Diện"}
            </H6>
            <Spacer height={sizes[4]} />
            {rows.map((row, rowIndex) => (
              <Content key={`row-${rowIndex}`}>
                {row.map((item, index) => (
                  <SessionQuickFireCard
                    session={item}
                    sessionNumber={item.session_number || ""}
                    key={`${item.conversation_id}-${index}`}
                  />
                ))}
              </Content>
            ))}
            {hasMoreItems && (
              <Btn onClick={goToSSHistory}>
                <H7 fontWeight="bold" color={colors.fwdOrange[100]}>
                  Xem tất cả báo cáo
                </H7>
              </Btn>
            )}
          </SessionView>
        )}
        <div ref={bottomRef} style={{ height: "1px" }} />
      </Container>
    </FullScreenPage>
  );
};

export default TGHomePage;
