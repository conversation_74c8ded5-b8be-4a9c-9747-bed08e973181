import React, { useEffect } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, H6, H7, Body, colors, sizes, Spacer } from "@components/CubeBaseElement";
import { useAppSelector, useAppDispatch } from "@store/index";
import { setUser } from "@store/appSlice";
import { device } from "@styles/media";
import { useExchangeCubeToken } from "../../hooks/useExchangeCubeToken";
import { useGetEcoachConfigurationData } from "../../hooks/useGetEcoachConfigurationData";
import { useReportHistoryLatest } from "../../hooks/useReportHistoryLatest";
import useMaintenance from "../../hooks/useMaintenance";
import FullScreenPage from "../../components/FullScreenPage";
import MaintenancePage from "../../components/Maintenance";
import NavigationCard, { NavigationCardType } from "../../components/cards/NavigationCard";
import SessionQuickFireCard from "../../components/cards/SessionQuickFireCard";
import CloseIcon from "../../assets/icons/CloseIcon";
import GoDownIcon from "../../assets/icons/GoDownIcon";
import { Rate } from "../../components/icons/Rate";
import {
  avatarMobile,
  avatarTablet,
  cafeBGMobile,
  cafeBGTablet,
  goDown
} from "@assets/index";

const Container = styled.div`
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );
  padding: ${sizes[4]}px;

  @media ${device.noMobile} {
    background: linear-gradient(
      0deg,
      #000 47%,
      rgba(0, 0, 0, 0) 65.55%,
      rgba(0, 0, 0, 0) 88.41%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
`;

const EntryPointBG = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${entryPointBgTablet});
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: -1;

  @media ${device.mobile} {
    background-image: url(${entryPointBgMobile});
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  gap: ${sizes[6]}px;
`;

const Title = styled(H3)`
  color: ${colors.white};
  margin-bottom: ${sizes[4]}px;
`;

const Subtitle = styled(H6)`
  color: ${colors.fwdGrey[50]};
  margin-bottom: ${sizes[8]}px;
`;

const ButtonGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  width: 100%;
  max-width: 300px;

  @media ${device.noMobile} {
    flex-direction: row;
    max-width: 500px;
  }
`;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.app);
  const { isTrainerGuruEnabled } = useAppSelector((state) => state.ecoach);

  // Initialize TGEntryPointCard logic
  const { cubeToken } = useExchangeCubeToken();
  const { ecoachConfig } = useGetEcoachConfigurationData(!!cubeToken);

  useEffect(() => {
    // Initialize user data if needed
    if (!user.name) {
      dispatch(setUser({
        channel: "AGENCY", // Allow TG card show for both AGENCY + BANCA channel
        email: "<EMAIL>",
        name: "Demo User",
        username: "demo",
        designation: "Agent",
        designationDesc: "Sales Agent",
      }));
    }
  }, [dispatch, user.name]);

  // TGEntryPointCard logic - check if ecoach should be available
  useEffect(() => {
    if (cubeToken && ecoachConfig) {
      console.log("ecoachConfig", ecoachConfig);
    }
  }, [cubeToken, ecoachConfig]);

  // Check if user can access ecoach (from TGEntryPointCard logic)
  const canAccessEcoach = () => {
    if (!cubeToken || !ecoachConfig) {
      return false;
    }

    // Allow TG card show for both AGENCY + BANCA channel
    if (!(user.channel === "BANCA" || user.channel === "AGENCY")) {
      return false;
    }

    if (!isTrainerGuruEnabled) {
      return false;
    }

    return true;
  };

  const handleStartQuickfire = () => {
    navigate("/product-selection?type=quickfire");
  };

  const handleStartFullExperience = () => {
    navigate("/product-selection?type=full");
  };

  const handleViewHistory = () => {
    navigate("/session-history");
  };

  const handleViewGuidelines = () => {
    navigate("/guidelines");
  };

  // Show loading state while checking access
  if (!cubeToken) {
    return (
      <Container>
        <EntryPointBG />
        <ContentWrapper>
          <Title>Loading Ecoach...</Title>
          <Subtitle>Checking access permissions...</Subtitle>
        </ContentWrapper>
      </Container>
    );
  }

  // Show access denied if user cannot access ecoach
  if (!canAccessEcoach()) {
    return (
      <Container>
        <EntryPointBG />
        <ContentWrapper>
          <Title>Access Restricted</Title>
          <Subtitle>
            Ecoach is not available for your account type or is currently disabled.
          </Subtitle>
        </ContentWrapper>
      </Container>
    );
  }

  return (
    <Container>
      <EntryPointBG />
      <ContentWrapper>
        <Title>Welcome to Ecoach</Title>
        <Subtitle>
          Practice your sales skills with AI-powered conversations
          {user.name && ` Hello, ${user.name}!`}
        </Subtitle>

        <ButtonGroup>
          <ButtonPrimary size="large" fullWidth onClick={handleStartQuickfire}>
            Start Quickfire Session
          </ButtonPrimary>
          <ButtonPrimary size="large" fullWidth onClick={handleStartFullExperience}>
            Start Full Experience
          </ButtonPrimary>
          <ButtonPrimary size="medium" fullWidth onClick={handleViewHistory}>
            View Session History
          </ButtonPrimary>
          <ButtonPrimary size="medium" fullWidth onClick={handleViewGuidelines}>
            View Guidelines
          </ButtonPrimary>
        </ButtonGroup>
      </ContentWrapper>
    </Container>
  );
};

export default HomePage;
