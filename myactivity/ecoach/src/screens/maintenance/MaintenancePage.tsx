import React from "react";
import styled from "styled-components";
import { colors, H6, LargeBody, sizes } from "../../components/CubeBaseElement";
import { homeTitleImg } from "../../assets";
import MaintenanceInProgressIcon from "../../components/icons/MaintenanceInProgressIcon";

const HomeTitleImg = styled.img({
  width: 90,
  height: 46,
  margin: 0,
  marginTop: -sizes[4],
});
const Container = styled.div(() => ({
  width: "100%",
  height: "100%",
  flex: 1,
  backgroundColor: colors.white,
}));
const PageContainer = styled.div(() => ({
  flex: 1,
  width: "100%",
  justifyContent: "center",
  alignItems: "center",
}));
const UnderMaintenanceText = styled(H6)({
  textAlign: "center",
  maxWidth: 180,
});

const ComeBackInAFewMinutesText = styled(LargeBody)(() => ({
  paddingTop: sizes[1],
  paddingBottom: sizes[12],
}));

const Header = styled.div`
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  margin-top: ${sizes[7]}px;
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
`;
const MaintenancePage = () => {
  return (
    <Container>
      <Header>
        <H6 fontWeight="bold" color={"#FEF9F4"}>
          Chuyên Gia{" "}
          <H6 fontWeight="bold" color={"#E87722"}>
            Đào Tạo
          </H6>
        </H6>
        <HomeTitleImg src={homeTitleImg} alt={""} />
      </Header>

      <PageContainer>
        <UnderMaintenanceText fontWeight={"bold"}>Hệ thống đang được bảo trì</UnderMaintenanceText>
        <ComeBackInAFewMinutesText color={colors.fwdOrange["100"]}>
          Vui lòng quay lại sau ít phút nữa
        </ComeBackInAFewMinutesText>
        <MaintenanceInProgressIcon />
      </PageContainer>
    </Container>
  );
};

export default MaintenancePage;
