import React from "react";
import styled from "styled-components";

const Container = styled.div<{ backgroundImage?: string }>`
  width: 100%;
  height: 100vh;
  position: relative;
  background-image: ${({ backgroundImage }) => 
    backgroundImage ? `url(${backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
`;

interface FullScreenPageProps {
  children: React.ReactNode;
  backgroundImage?: string;
}

const FullScreenPage: React.FC<FullScreenPageProps> = ({
  children,
  backgroundImage,
}) => {
  return (
    <Container backgroundImage={backgroundImage}>
      {children}
    </Container>
  );
};

export default FullScreenPage;
