import Portal from "./portal";
import ScrollBar from "./scroll-bar";
import styled from "styled-components";
import { LeftHeader, TitleHeader } from "./modal-full-page/styled";
import { ButtonIcon } from "@styles/buttons";
import Icons from "./icons";
import { Spacing } from "../styles";
import { colors } from "./CubeBaseElement";

interface ModalFullPageProps {
  children: JSX.Element | JSX.Element[];
  showHeader?: boolean;
  title?: any;
  goBack?: () => void;
  backgroundImage?: string;
}

const ModalFullPageWrapper = styled.div<{ showFooter?: boolean; backgroundImage?: string }>`
  width: 100vw;
  height: 100vh;
  padding-bottom: ${({ showFooter }) => (showFooter ? "84px" : "")};
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;
  overflow: auto;
  background: #e5e5e5;

  :after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -2;

    background: #e5e5e5;
    background-image: url(${(props) => props.backgroundImage});
    background-repeat: no-repeat;
    background-size: cover;
  }
`;

const Header = styled.div`
  width: 100%;
  padding: 15px 18px;
  position: fixed;
  top: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: transparent;
`;

const FullScreenPage = ({ children, showHeader, title, goBack, backgroundImage }: ModalFullPageProps) => {
  return (
    <Portal>
      <ModalFullPageWrapper showFooter={false} backgroundImage={backgroundImage}>
        {showHeader && (
          <Header>
            <LeftHeader>
              {goBack && (
                <ButtonIcon onClick={goBack}>
                  <Icons icon="arrow-left-in-header" fill={colors.white} />
                </ButtonIcon>
              )}
              {title && (
                <TitleHeader>
                  <h6>{title}</h6>
                  <Spacing />
                </TitleHeader>
              )}
            </LeftHeader>
          </Header>
        )}
        <ScrollBar>{children}</ScrollBar>
      </ModalFullPageWrapper>
    </Portal>
  );
};

export default FullScreenPage;
