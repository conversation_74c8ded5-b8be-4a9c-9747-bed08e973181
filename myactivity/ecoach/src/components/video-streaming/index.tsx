import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import useWindowResize from "@hooks/use-window-resize";
import { useAppSelector } from "@store/hooks";

type VideoStreamingProps = {
  conversationId: string;
  isFocused: boolean;
  avatarId: string;
  isTimeOut: boolean;
  style?: any;
};

const Container = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex: 1;
  width: 100%;
  height: 100%;
  background-color: black;
`;

const VideoView = styled.div`
  width: 100%;
  flex: 1;
`;

const VideoStreaming = ({ isFocused, avatarId, isTimeOut, style }: VideoStreamingProps) => {
  const { width = 0, height = 0 } = useWindowResize();
  const {
    app: {
      user: { name: agentNameParam },
    },
    ecoach: { cubeToken },
  } = useAppSelector((state) => state);
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const wsIntervalRef = useRef<any>(null);
  const wsTimeoutRef = useRef<any>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const [remoteMediaStream, setRemoteMediaStream] = useState<MediaStream | null>(null);

  const frameWidth = width < 750 ? Math.round((height * 16) / 9) : width;

  const setupWebSocket = async () => {
    const openWebSocket = async (token: string) => {
      // console.log('VideoStreaming openWebSocket token:', token);
      const url = `${import.meta.env.VITE_ECOACH_PUBLIC_AVATAR_WEBSOCKET_URL}${
        avatarId ? `/${avatarId}` : ""
      }?access_token=${cubeToken}&country=vn`;
      const ws = new WebSocket(url);

      ws.onopen = () => {
        ws.send(
          JSON.stringify({
            type: "listStreamers",
            streamerId: "DefaultStreamer",
          })
        );
      };

      ws.onclose = async (event) => {
        // Handle 403 error (Forbidden) for token expiration
        // if (event.code === 1008 || event.reason.includes("403")) {
        //   const loginWithRefreshRes = await loginWithRefreshToken(refreshToken || "");
        //   if (loginWithRefreshRes.status !== 200) {
        //     // console.error('Failed to refresh token');
        //     return;
        //   }
        //   const { access_token, refresh_token, id_token } = loginWithRefreshRes.data;
        //   login(access_token, refresh_token, id_token);
        //   // Reopen WebSocket with the new access token
        //   openWebSocket(access_token);
        // }
      };

      ws.onmessage = async (event) => {
        const message = JSON.parse(event.data);
        switch (message.type) {
          case "offer":
            await handleOffer(ws, message.sdp);
            break;
          case "iceCandidate":
            handleIceCandidate(message.candidate);
            break;
          case "streamerList":
            ws.send(
              JSON.stringify({
                type: "subscribe",
                streamerId: message["ids"][0],
              })
            );
            break;
          default:
            break;
        }
      };
      wsRef.current = ws;
    };

    const handleOffer = async (ws: WebSocket, offerSDP: string) => {
      const pcConfig = {
        iceServers: [
          {
            urls: import.meta.env.VITE_ECOACH_TURN_SERVER_URL,
            username: import.meta.env.VITE_ECOACH_TURN_SERVER_USER,
            credential: import.meta.env.VITE_ECOACH_TURN_SERVER_PASS,
          },
        ],
      };

      const pc = new RTCPeerConnection(pcConfig);

      pc.addEventListener("onicecandidate", (event: any) => {
        if (event.candidate) {
          ws.send(
            JSON.stringify({
              type: "iceCandidate",
              candidate: event.candidate,
            })
          );
        }
      });
      pc.addEventListener("track", (event) => {
        if (event.streams && event.streams[0] && remoteVideoRef.current) {
          remoteVideoRef.current.srcObject = event.streams[0];
        }

        event.streams[0].getTracks().forEach((track) => {
          if (track.kind === "video") {
            const reStream = new MediaStream([]);
            reStream.addTrack(track);
            setRemoteMediaStream(reStream);
          }
        });
      });

      await pc.setRemoteDescription({ type: "offer", sdp: offerSDP });
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      pcRef.current = pc;

      ws.send(JSON.stringify(pc.localDescription));
    };

    const handleIceCandidate = (candidate: RTCIceCandidate) => {
      const pc = pcRef.current;
      if (!pc) return;

      pc.addIceCandidate(new RTCIceCandidate(candidate))
        .then(() => {
          console.log("ICE candidate added successfully");
        })
        .catch((error) => {
          console.error("Failed to add ICE candidate:", error);
        });
    };

    openWebSocket("");
  };

  useEffect(() => {
    if (avatarId) {
      pcRef.current?.close();
      setRemoteMediaStream(null);
      setupWebSocket();

      wsIntervalRef.current = setInterval(() => {
        try {
          wsRef.current?.send(
            JSON.stringify({
              type: "stats",
              data: avatarId,
            })
          );
        } catch (error) {
          console.log(error);
        }
      }, 20 * 1000);

      // Reconnect after 9 mins
      wsTimeoutRef.current = setTimeout(() => {
        try {
          setupWebSocket();
        } catch (error) {
          console.log(error);
        }
      }, 550000); // 9 mins 10 seconds refresh to prevent crash with 20s stats refresh
    }

    return () => {
      if (wsIntervalRef.current) {
        clearInterval(wsIntervalRef.current);
      }
      if (wsTimeoutRef.current) {
        clearInterval(wsTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (pcRef.current) {
        pcRef.current.close();
      }
    };
  }, [avatarId]);

  useEffect(() => {
    if (!isFocused) {
      pcRef.current?.close();
      setRemoteMediaStream(null);
    }
  }, [isFocused]);

  useEffect(() => {
    if (isTimeOut) {
      if (wsIntervalRef.current) {
        clearInterval(wsIntervalRef.current);
      }
      if (wsTimeoutRef.current) {
        clearInterval(wsTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (pcRef.current) {
        pcRef.current.close();
      }
    }
  }, [isTimeOut]);

  return (
    <Container
      style={
        style || width < 750
          ? {
              width: frameWidth,
              transform: `translate(-${frameWidth / 2 - width / 2 - 25}px, 0)`,
            }
          : {}
      }
    >
      <video
        style={{ width: "100%", height: "100%" }}
        ref={remoteVideoRef}
        x-webkit-airplay="allow"
        autoPlay
        muted
        playsInline
      ></video>
    </Container>
  );
};

export default VideoStreaming;
