"use client";

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import request from "api";
import { getWebSocketParam } from "../../ultils/videoCallHelper";
import { useSearchParams } from "next/navigation";
import { DifficultType, ProductFlowType } from "../../@custom-types/ecoach";
import { useAppSelector } from "../../../../hooks/use-redux";

type WebRTCProps = {
  conversationId: string;
  muted: boolean;
  isTimeOut: boolean;
  productSelectionCode: string;
  productFlowType: ProductFlowType;
  onSocketConnected: () => void;
  onSocketClosed?: () => void;
  setAvatarIsReady: React.Dispatch<React.SetStateAction<boolean>>;
  setAvatarUuid: React.Dispatch<React.SetStateAction<string>>;
  setThinkingState: React.Dispatch<React.SetStateAction<boolean>>;
  setWebSocket?: React.Dispatch<React.SetStateAction<WebSocket | null>>;
  setHeartScore?: React.Dispatch<React.SetStateAction<number>>;
  setAvatarName?: React.Dispatch<React.SetStateAction<string>>;
  setEndCallFlag?: React.Dispatch<React.SetStateAction<boolean>>;
};

interface PeerConfiguration {
  iceServers?: any; // Replace 'any' with the appropriate type if known
}

const WebRTC = forwardRef<{ close: () => void }, WebRTCProps>(
  (
    {
      conversationId,
      muted,
      onSocketConnected,
      onSocketClosed,
      isTimeOut,
      setAvatarIsReady,
      setAvatarUuid,
      setThinkingState,
      setWebSocket,
      setHeartScore,
      setAvatarName,
      setEndCallFlag,
    },
    ref
  ) => {
    const remoteAudioRef = useRef<HTMLAudioElement>(null);
    const remoteVideoRef = useRef<HTMLVideoElement>(null);
    const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
    const wsRef = useRef<WebSocket | null>(null);
    const audioTrackRef = useRef<MediaStreamTrack | null>(null);
    const mutedRef = useRef(muted);
    const searchParams = useSearchParams();

    const productFlowType = searchParams.get("productFlowType");
    const productSelectionCode = searchParams.get("productSelectionCode");
    const difficultType = searchParams.get("difficultType") as DifficultType;

    const {
      rootReducer: {
        user: { name: agentNameParam },
      },
      ecoachReducer: { cubeToken },
    } = useAppSelector((state) => state);

    useImperativeHandle(
      ref,
      () => ({
        close(done?: () => void) {
          try {
            audioTrackRef.current?.stop();
            wsRef.current?.close();
            peerConnectionRef.current?.close();

            waitForSocketClose(wsRef.current, done);
          } catch (e) {
            console.error("WebRTC Cleanup: ", e);
          }
        },
        endGraceful() {
          if (wsRef.current) {
            wsRef.current.send(
              JSON.stringify({
                type: "status",
                content: "ended",
              })
            );
          }
        },
        retake(conversationId: string) {
          this.close(() => {
            setupWebsocket(conversationId, async () => {
              await setupWebRTCConnection();
            });
          });
        },
      }),
      []
    );

    useEffect(() => {
      if (audioTrackRef.current) {
        audioTrackRef.current.enabled = !muted;
      }
    }, [muted]);

    useEffect(() => {
      mutedRef.current = muted;
    }, [muted]);

    useEffect(() => {
      if (isTimeOut) {
        audioTrackRef.current?.stop();
        wsRef.current?.close();
        peerConnectionRef.current?.close();
      }
    }, [isTimeOut]);

    useEffect(() => {
      setupWebsocket(conversationId, async () => {
        await setupWebRTCConnection();
      });

      return () => {
        try {
          audioTrackRef.current?.stop();

          wsRef.current?.close();
          peerConnectionRef.current?.close();

          wsRef.current = null;
          peerConnectionRef.current = null;
        } catch (e) {
          console.error("WebRTC Cleanup: ", e);
        }
      };
    }, []);

    const handleOffer = async (offer: RTCSessionDescriptionInit) => {
      const peerConnection = peerConnectionRef.current;
      if (peerConnection) {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);
        wsRef.current.send(
          JSON.stringify({
            type: "answer",
            answer: peerConnection.localDescription,
          })
        );
      }
    };

    const handleAnswer = async (answer: RTCSessionDescriptionInit) => {
      const peerConnection = peerConnectionRef.current;
      if (peerConnection) {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
      }

      try {
        if (productFlowType === ProductFlowType.QUICKFIRE) {
          wsRef.current.send(
            JSON.stringify({
              type: "cube_app_audio_ready",
            })
          );
        }
      } catch (e) {
        console.error("Error sending answer:", e);
      }
    };

    const handleRestart = async () => setupWebRTCConnection();

    const handleIceCandidate = (candidate: RTCIceCandidateInit) => {
      const peerConnection = peerConnectionRef.current;
      if (peerConnection) {
        peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
      }
    };

    const handleWebSocketMessages = (event: MessageEvent) => {
      const message = JSON.parse(event.data);

      switch (message.type) {
        case "offer":
          handleOffer(message.offer);
          break;
        case "answer":
          handleAnswer(message.answer);
          break;
        case "restart":
          handleRestart();
          break;
        case "ice-candidate":
          handleIceCandidate(message.candidate);
          break;
        case "status":
          if (message.status === "ready" && setAvatarIsReady) {
            setAvatarIsReady(true);

            if (setAvatarUuid && message.avatar_uuid) {
              setAvatarUuid(message.avatar_uuid);
            }
          }
          break;
        case "thinkingState":
          setThinkingState && setThinkingState(!!message.thinking);
          break;
        case "heart":
          setHeartScore && setHeartScore(message.count);
          break;
        case "end_call":
          setEndCallFlag && setEndCallFlag(message.output);
          break;
        case "name":
          setAvatarName && setAvatarName(message.name);
          break;
        case "audio":
          // pushAudioToQueue(message.message); // Add audio to the queue
          break;

        case "interrupt":
          // stopAudio();
          break;

        default:
          console.log("Received unknown message type:", message.type);
      }
    };

    const waitForSocketClose = (socket: WebSocket, callback: () => void) => {
      setTimeout(function () {
        if (socket.readyState === WebSocket.CLOSED) {
          console.log("Connection is closed");
          onSocketClosed?.();
          callback?.();
        } else {
          waitForSocketClose(socket, callback);
        }
      }, 10);
    };

    const waitForSocketConnection = (socket: WebSocket, callback: () => void) => {
      setTimeout(function () {
        if (socket.readyState === WebSocket.OPEN) {
          console.log("Connection is made");
          onSocketConnected?.();
          callback?.();
        } else {
          console.log("wait for connection...");
          waitForSocketConnection(socket, callback);
        }
      }, 10);
    };

    const setupWebsocket = (conversationId: string, done?: () => void) => {
      if (wsRef.current) {
        wsRef.current = null;
      }

      wsRef.current = new WebSocket(
        `${
          import.meta.env.VITE_ECOACH_PUBLIC_CONVERSATION_WEBSOCKET_URL
        }${conversationId}?access_token=${cubeToken}&country=${"vn"}`
      );

      wsRef.current.onopen = async (event) => {
        if (setWebSocket!) {
          setWebSocket(wsRef.current);
        }

        const param = await getWebSocketParam({
          productSelectionCode,
          productFlowType,
          difficultType,
          agentName: agentNameParam || "Meggie",
          ...searchParams,
        });

        wsRef.current?.send(JSON.stringify({ type: "setup", param }));

        waitForSocketConnection(wsRef.current, done);
      };

      wsRef.current.addEventListener("message", handleWebSocketMessages);
    };

    const setupWebRTCConnection = async () => {
      try {
        if (!wsRef.current) {
          return;
        }

        if (peerConnectionRef.current) {
          peerConnectionRef.current.close();
          peerConnectionRef.current = null;
        }

        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
          },
          video: false,
        });

        const peerConnection = new RTCPeerConnection({
          iceServers: [
            {
              urls: import.meta.env.VITE_ECOACH_TURN_SERVER_URL,
              username: import.meta.env.VITE_ECOACH_TURN_SERVER_USER,
              credential: import.meta.env.VITE_ECOACH_TURN_SERVER_PASS,
            },
          ],
        });

        stream.getTracks().forEach((track) => {
          peerConnection.addTrack(track, stream);

          if (track.kind === "audio") {
            audioTrackRef.current = track;

            if (audioTrackRef.current) {
              audioTrackRef.current.enabled = !mutedRef.current;
            }
          }
        });

        const offer = await peerConnection.createOffer({});
        await peerConnection.setLocalDescription(offer);
        wsRef.current.send(
          JSON.stringify({
            type: "offer",
            offer: peerConnection.localDescription,
          })
        );

        peerConnection.onicecandidate = (event) => {
          if (event.candidate) {
            const ipAddress = event.candidate.candidate.split(" ")[4];
            wsRef.current?.send(
              JSON.stringify({
                type: "ice-candidate",
                candidate: {
                  address: event.candidate.address,
                  candidate: event.candidate.candidate,
                  component: event.candidate.component,
                  foundation: event.candidate.foundation,
                  ip: ipAddress,
                  port: event.candidate.port,
                  priority: event.candidate.priority,
                  protocol: event.candidate.protocol,
                  relatedAddress: event.candidate.relatedAddress,
                  relatedPort: event.candidate.relatedPort,
                  sdpMLineIndex: event.candidate.sdpMLineIndex,
                  sdpMid: event.candidate.sdpMid,
                  tcpType: event.candidate.tcpType,
                  type: event.candidate.type,
                  usernameFragment: event.candidate.usernameFragment,
                },
              })
            );
          }
        };

        peerConnection.addEventListener("icegatheringstatechange", (event) => {
          console.log("peerConnection.iceGatheringState", peerConnection.iceGatheringState);
        });

        // Handle incoming remote tracks (video and audio)
        peerConnection.ontrack = (event) => {
          if (
            event.streams &&
            event.streams[0] &&
            event.streams[0].getAudioTracks().length > 0 &&
            remoteAudioRef.current
          ) {
            remoteAudioRef.current.srcObject = event.streams[0];
          }
        };

        peerConnectionRef.current = peerConnection;
      } catch (error) {
        console.error("Error setting up WebRTC connection:", error);
      }
    };

    return (
      <div>
        <audio ref={remoteAudioRef} autoPlay></audio>
      </div>
    );
  }
);
WebRTC.displayName = "WebRTC";
export default WebRTC;
