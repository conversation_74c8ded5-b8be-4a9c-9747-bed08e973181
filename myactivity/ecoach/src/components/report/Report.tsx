"use client";

import React from "react";
import dayjs from "dayjs";
import StarIcon from "@components/icons/StarIcon";
import StarFillIcon from "@components/icons/StarFillIcon";
// TODO: Import ReportDetails from correct location
// import { extractScore, renderSkillTitle, ReportDetails } from "screens/ecoach/components/report/ReportDetails";
import { useNavigate, useSearchParams } from "react-router-dom";
import bg from "../../assets/report-bg.png";
import {
  ButtonContainer,
  ButtonInner,
  ChevronRightIcon,
  CurrentScoreTitle,
  FinishButton,
  InforPane,
  InforPaneInner,
  InforPaneItem,
  OverallScoreTitle,
  ReportBackground,
  ReportContainer,
  ReportContent,
  ReportContentContainer,
  ReportContentInner,
  ReportGridContainer,
  ReportInner,
  ReportItem,
  ReportItemContainer,
  ScoreBar,
  ScoreBarContainer,
  ScoreBarInner,
  ScoreBarParent,
  ScoreNumber,
  SeeMoreLink,
  <PERSON>llContainer,
  <PERSON>ll<PERSON><PERSON>leContaine<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>IconElement,
  YourReportContainer,
  YourReportTitle,
} from "./styled";

type ReportProps = {
  reportDetails: {
    conversation_id: string;
    datetime: string;
    duration: string;
    difficulty: string;
    report_is_ready: "true" | "false";
    report: ReportDetails;
  };
  onSkillSelected?: (v: boolean) => void;
};

const Report: React.FC<ReportProps> = ({ reportDetails, onSkillSelected }) => {
  // const router = useRouter(); // TODO: Fix router usage
  const searchParams = useSearchParams();

  if (!reportDetails || !reportDetails?.report) {
    return null;
  }

  const { overall_score } = reportDetails.report || {};

  const skillScores: { [key: string]: string } = {};

  Object.entries(reportDetails.report.skill_set_details).map(([key, value]) => {
    skillScores[key] = (reportDetails as any).report[`${key}_score`];
  });

  const gotoDetails = (skill: string, event: any) => {
    event.preventDefault();

    const skillSet = (reportDetails as any).report.skill_set_details[skill];

    router.push(
      `/ecoach/report/details?${searchParams.toString()}&skill=${skill}&skillSet=${encodeURIComponent(
        JSON.stringify(skillSet)
      )}`
    );
  };

  const handleClose = () => {
    router.push("/ecoach");
  };

  const renderScoreBar = (skill: any, scoreString: string, color: string) => {
    if (!scoreString) {
      return null;
    }

    const score = extractScore(scoreString);
    let bgColor = score < 30 ? "#DA0303" : color;
    let textColor = score < 30 ? "#DA0303" : "#183028";

    return (
      <ScoreBarInner className={"flex-1 relative h-full w-full"}>
        <ScoreNumber
          className={"w-full bottom-0 absolute text-[2rem] score-title"}
          style={{ height: `calc(${score}% + 3.5rem)`, color: textColor }}
        >
          {score}
        </ScoreNumber>
        <ScoreBar
          className={"rounded-xl overflow-hidden w-full h-full absolute bottom-0 score1"}
          data-score={score}
          style={{ height: score + "%", color: textColor, backgroundColor: bgColor }}
        ></ScoreBar>
      </ScoreBarInner>
    );
  };

  const renderBottomText = (skill: any, scoreString: string) => {
    if (!scoreString) {
      return null;
    }
    return (
      <SeeMoreLink
        href={"#details"}
        onClick={(event) => gotoDetails(skill, event)}
        className={"text-2xl text-[#E87722] text1-left flex items-center ml1-4 more-details"}
      >
        <span className={"flex"}>
          {extractScore(scoreString) < 50
            ? "Xem thêm chi tiết" //'See how you can do better'
            : "Xem thêm chi tiết"}
        </span>{" "}
        <ChevronRightIcon />
      </SeeMoreLink>
    );
  };

  const renderStar = (overallScore: number) => {
    const starWidth = 120;

    if (overallScore < 14)
      return (
        <>
          <Star width={120} height={120} />
          <Star width={120} height={120} />
          <Star width={120} height={120} />
        </>
      );
    else if (overallScore >= 14 && overallScore < 28)
      return (
        <>
          <StarHalf width={120} height={120} />
          <Star width={120} height={120} />
          <Star width={120} height={120} />
        </>
      );
    else if (overallScore >= 28 && overallScore < 42)
      return (
        <>
          <StarFill width={120} height={120} />
          <Star width={120} height={120} />
          <Star width={120} height={120} />
        </>
      );
    else if (overallScore >= 42 && overallScore < 56)
      return (
        <>
          <StarFill width={120} height={120} />
          <StarHalf width={120} height={120} />
          <Star width={120} height={120} />
        </>
      );
    else if (overallScore >= 56 && overallScore < 70)
      return (
        <>
          <StarFill width={120} height={120} />
          <StarFill width={120} height={120} />
          <Star width={120} height={120} />
        </>
      );
    else if (overallScore >= 70 && overallScore < 84)
      return (
        <>
          <StarFill width={120} height={120} />
          <StarFill width={120} height={120} />
          <StarHalf width={120} height={120} />
        </>
      );
    else if (overallScore >= 84)
      return (
        <>
          <StarFill width={120} height={120} />
          <StarFill width={120} height={120} />
          <StarFill width={120} height={120} />
        </>
      );
    return (
      <>
        <Star width={120} height={120} />
        <Star width={120} height={120} />
        <Star width={120} height={120} />
      </>
    );
  };

  return (
    <ReportContainer className="report-loading min-h-screen">
      <ReportInner className={"flex w-full h-full min-h-screen bg-[#fffbf6] items-center justify-center"}>
        <ReportBackground
          alt={"loading"}
          src={bg}
          width={8000}
          className={"bg-cover bg-bottom self-end absolute left-0 right-0 h-auto"}

        />
        <ReportContent className={"flex flex-col items-center justify-center z-10 mt"}>
          <ReportContentInner className={"w-full flex items-center justify-between overall-score-container"}>
            <YourReportContainer className={"flex items-start justify-center h-full self-start your-report-container"}>
              <YourReportTitle className={"font-bold text-[4.5rem] your-report"}>Báo Cáo Của Bạn</YourReportTitle>
              <WeatherIconElement className={"ml-8 weather"} style={{ transform: "scale(1.4)" }} />
            </YourReportContainer>
            <StartContainer className={"mb-4 text-center flex items-center justify-center star"}>
              {renderStar(extractScore(overall_score))}
            </StartContainer>
            <div className={"text-left"}>
              <OverallScoreTitle className={"text-4xl text-[#8B9793] font-bold overall-score"}>
                TỔNG ĐIỂM
              </OverallScoreTitle>
              <CurrentScoreTitle className={"font-bold text-left leading-[100px] current-score-container"}>
                <span className={"text-[6rem] text-[#183028] mr-1 current-score"}>{extractScore(overall_score)}</span>
                <span className={"text-2xl text-[#8B9793] total-score"}>/100</span>
              </CurrentScoreTitle>
            </div>
          </ReportContentInner>
          <ReportContentContainer
            className={"w-full text-center items-center max-w-[1820px] rounded-lg mb-1 report-container"}
          >
            <ReportItemContainer
              className={"flex-1 w-full h-full mask relative rounded-3xl"}
              style={{
                boxShadow: "0px 0px 10px 0px #00000040",
              }}
            >
              <ReportGridContainer className={"w-full h-full rounded-lg p overflow-hidden report-grid-container"}>
                <ReportItem className={"grid gap-1 grid-cols-[repeat(5,minmax(320px,1fr))] min-h-[625px] report-item"}>
                  {Object.entries(skillScores).map(
                    ([skill, scoreString]) =>
                      skill !== "__product_knowledge" && (
                        <SkillContainer
                          className={
                            "rounded-lg flex flex-col items-center justify-center gap-2 w-full h-[calc(100%-4rem)] cursor-pointer mt"
                          }
                          key={skill}
                          onClick={(event) => gotoDetails(skill, event)}
                        >
                          <ScoreBarParent className={"flex-1 h-full w-full"}>
                            <ScoreBarContainer
                              className={
                                "flex flex-col h-full items-center justify-center gap-2 mb-2 px-14 score-bar-container"
                              }
                            >
                              {renderScoreBar(
                                skill,
                                scoreString,
                                skill === "objection_handling"
                                  ? "#0097A9"
                                  : skill === "relationship_management"
                                  ? "#6ECEB2"
                                  : skill === "communication_skills"
                                  ? "#7FCBD4"
                                  : "#B6E6D8"
                              )}
                            </ScoreBarContainer>
                          </ScoreBarParent>
                          <SkillTitleContainer className={"text-[1.8rem] text-center font-semibold skill-title"}>
                            {renderSkillTitle(skill)}
                          </SkillTitleContainer>
                          {renderBottomText(skill, scoreString)}
                        </SkillContainer>
                      )
                  )}
                </ReportItem>
                <ButtonContainer className={"w-full text-left"}>
                  <ButtonInner className={"flex items-start"}>
                    <FinishButton
                      onClick={handleClose}
                      className="finish-button max-w-full w-full h-[8.5rem] text-4xl m-auto mt-4 text-white bg-[#E87722] hover:bg-[#E87722] focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-md px-5 py-3"
                    >
                      Hoàn Thành
                    </FinishButton>
                  </ButtonInner>
                </ButtonContainer>
              </ReportGridContainer>
            </ReportItemContainer>
          </ReportContentContainer>

          <InforPane className={"w-full text-left mb-2"}>
            <InforPaneInner className={"flex items-start gap-2"}>
              <div>
                <InforPaneItem className={"text-gray-300 text-[8px]"}>
                  ID: <span className={"font-semibold"}>{reportDetails.conversation_id}</span>
                </InforPaneItem>
              </div>
              <div>
                <InforPaneItem className={"text-gray-300 text-[8px]"}>
                  Ngày và Giờ:{" "}
                  <span className={"font-semibold"}>{dayjs(reportDetails.datetime).format("DD MMM YYYY")}</span>
                </InforPaneItem>
              </div>
            </InforPaneInner>
          </InforPane>
        </ReportContent>
      </ReportInner>
    </ReportContainer>
  );
};

export default Report;
