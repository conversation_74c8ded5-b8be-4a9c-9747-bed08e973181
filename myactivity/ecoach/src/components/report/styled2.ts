import styled from "styled-components";
import { colors, H3, H5, H7, H8, Label, sizes } from "../CubeBaseElement";
// import Image from "next/image"; // Removed NextJS dependency
import { extractScore } from "../../ultils/extractScore";

const media = {
  mobile: `(max-width: 910px)`,
  noMobile: `(min-width: 911px)`,
};

export const MIN_SCORE = 50;

export const ReportContent = styled.div`
  padding-left: ${sizes[8]}px;
  padding-right: ${sizes[8]}px;
  margin: ${sizes[4]}px;

  @media ${media.mobile} {
    padding-left: ${sizes[1]}px;
    padding-right: ${sizes[1]}px;
    margin: ${sizes[1]}px;
  }
`;

export const HorizontalView = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  //justify-content: space-between;
  gap: ${sizes[1]}px;
  position: relative;
  z-index: 100;
`;
export const BtnGroupView = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  max-width: 400px;
  padding-top: ${sizes[8]}px;
  padding: ${sizes[1]}px;
  gap: ${sizes[4]}px;
  margin-bottom: ${sizes[10]}px;

  @media ${media.mobile} {
    margin-bottom: ${sizes[5]}px;
    width: 100%;
  }
`;
export const LeftView = styled.div`
  align-items: flex-start;
  gap: ${sizes[1]}px;
`;
export const RightView = styled.div`
  align-items: flex-start;
  gap: ${sizes[1]}px;
  flex-grow: 1;
  max-width: 50%;
`;
export const PageImageBackground = styled.img`
  flex: 1;
  width: 100%;
  background-size: cover;
  background-position: bottom;
  align-self: end;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
`;
export const WeatherIconContainer = styled.div`
  position: absolute;
  right: 0;
  top: ${sizes[12]}px;
  background-color: transparent;

  @media ${media.mobile} {
    top: ${sizes[14]}px;
    svg {
      width: 135px;
      height: 61px;
    }
  }
`;
export const YourReportText = styled(H5)`
  color: ${colors.fwdDarkGreen[100]};
  padding-bottom: ${sizes[6]}px;
`;
export const DifficultyTypeTitle = styled(H8)`
  color: ${colors.fwdDarkGreen[100]};
`;
export const ScoreText = styled(Label)`
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  margin-top: ${sizes[2]}px;

  @media ${media.noMobile} {
    margin-top: ${sizes[1]}px;
  }
`;
export const BackBtn = styled.button`
  padding-left: ${sizes[4]}px;
  padding-right: ${sizes[4]}px;
`;
export const LoadingContainer = styled.div`
  flex: 1;
`;
export const OverallScoreContainer = styled.div`
  padding: ${sizes[12]}px;
  margin-top: ${sizes[6]}px;
  border-radius: ${sizes[2]}px;
  position: relative;
  min-height: ${sizes[25]}px;
  width: 260px;
  border-color: white;
  background-color: ${colors.fwdDarkGreen[100]};
  text-align: center;

  @media ${media.mobile} {
    display: none;
  }
`;

export const OverallScoreContainerMobile = styled.div`
  padding: ${sizes[4]}px;
  margin-top: ${sizes[1]}px;
  border-radius: ${sizes[2]}px;
  position: relative;
  min-height: ${sizes[25]}px;
  width: 100%;
  border-color: white;
  background-color: ${colors.fwdDarkGreen[100]};
  text-align: center;

  @media ${media.noMobile} {
    display: none;
  }
`;

export const StarContainer = styled.div`
  margin-bottom: ${sizes[4]}px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;

  @media ${media.mobile} {
    margin-bottom: ${sizes[2]}px;
  }
`;
export const HeartScore = styled.div`
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: center;

  @media ${media.mobile} {
    margin-top: -${sizes[2]}px;
  }
`;
export const CurrentScore = styled(H3)`
  text-align: center;
  margin-right: ${sizes[1]}px;
`;
export const TotalScore = styled(H7)`
  text-align: center;
`;
export const IconGroupFriendsContainer = styled.div`
  margin-left: -${sizes[12]}px;
  position: absolute;
  bottom: 0;
`;
export const IconGroupFriends2Container = styled.div`
  margin-left: -${sizes[8]}px;
  position: absolute;
  bottom: 0;
`;
export const IconSkateBoardContainer = styled.div`
  position: absolute;
  bottom: -${sizes[1]}px;
  right: -${sizes[1]}px;

  @media ${media.mobile} {
    display: none;
  }
`;
export const ScoreSectionContainer = styled.div`
  padding: ${sizes[1]}px;
  margin-top: ${sizes[6]}px;
  flex: 1;
  height: 100%;
  z-index: 10;

  @media ${media.mobile} {
    position: relative;
  }
`;
export const ScoreSectionBorder = styled.div`
  padding: ${sizes[1]}px;
  border-radius: ${sizes[2]}px;
  overflow: hidden;
  background: linear-gradient(126.57deg, #6eceb2 11.93%, #fed241 33.49%, #e87722 73.6%, #0097a9 100%);
`;
export const ScoreSection = styled.div`
  width: 100%;
  background-color: ${colors.fwdDarkGreen[100]};
  padding: ${sizes[8]}px;
  border-radius: ${sizes[2]}px;

  @media ${media.mobile} {
    padding: ${sizes[1]}px;
  }
`;
export const ScoreContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-wrap: wrap;
  position: relative;

  @media ${media.mobile} {
    display: block;
    padding-bottom: ${sizes[3]}px;
  }
`;
export const PrimaryScoreContainer = styled.div`
  padding-top: ${sizes[2]}px;
  margin-left: ${sizes[4]}px;
  margin-right: ${sizes[4]}px;
`;
export const ScoreItem = styled.div`
  width: 50%;
  padding: ${sizes[4]}px;

  @media ${media.mobile} {
    width: 100%;
    padding: 2px ${sizes[4]}px;
  }
`;
export const ScoreSectionInner = styled.div<{
  score?: string;
  borderColor?: string;
  borderWidth?: number;
  marginBottom?: number;
}>`
  cursor: pointer;
  border-style: solid;
  border-radius: ${sizes[2]}px;
  border-width: ${({ borderWidth }) => borderWidth}px;
  border-color: ${({ borderColor, score }) =>
    borderColor ? borderColor : extractScore(score) < 50 ? "#F34A4A" : colors.fwdGrey[100]};
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: ${sizes[4]}px;
  padding: ${sizes[4]}px;
  width: 100%;
  margin-bottom: ${({ marginBottom }) => (marginBottom !== undefined ? marginBottom : sizes[8])}px;

  @media ${media.mobile} {
    margin-bottom: 0;
  }
`;

export const ScoreProductTitle = styled.div`
  width: ${sizes[28]}px;

  @media ${media.mobile} {
    width: 30%;
    max-width: 30%;
  }
`;
export const ScoreProductBar = styled.div`
  flex: 1;
  max-width: ${sizes[60]}px;
`;
export const ScoreBarContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: ${sizes[2]}px;
  margin-bottom: ${sizes[2]}px;
`;
export const ScoreBar = styled.div<{ color: string }>`
  border-color: ${({ color }) => color};
  flex: 1;
  border-radius: ${sizes[1]}px;
  border-width: 1px;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: ${sizes[8]}px;
`;

export const ScoreBarTrack = styled.div<{ score: number; color: string }>`
  width: ${({ score }) => score}%;
  background-color: ${({ color }) => color};
  height: 100%;
`;

export const SeeMoreContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex: 1;
  gap: ${sizes[3]}px;
`;

export const SeeMoreContent = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  gap: ${sizes[2]}px;
`;

export const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;
