import { IconProperty } from "../../types/custom-types";

export const Rate = ({ width = 32, height = 32, fill = "none" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_6398_35075)">
        <path
          d="M22.0503 2H13.3403C12.9203 2 12.5703 2.35 12.5703 2.77V10.19C12.5703 10.69 12.8603 11.13 13.3203 11.31C13.4603 11.38 13.6203 11.41 13.8003 11.41C14.1303 11.41 14.4303 11.29 14.6603 11.05L15.8103 9.9H22.0503C22.4703 9.9 22.8103 9.56 22.8103 9.14V2.77C22.8103 2.35 22.4703 2 22.0503 2ZM20.8203 7.92H15.3003C15.0903 7.92 14.8903 8 14.7703 8.14L14.5703 8.34V3.98H20.8203V7.92Z"
          fill="white"
        />
        <path
          d="M16.93 13.19C16.46 12.62 15.76 12.3 15.03 12.3H11.81V9.63996C11.81 7.96996 10.14 7.20996 8.6 7.20996C8.06 7.20996 7.62 7.64996 7.62 8.18996C7.62 9.62996 7.35 10.94 7.17 11.12L5.22 13.02H1.98C1.44 13.02 1 13.46 1 14V21.26C1 21.8 1.44 22.24 1.98 22.24H14.09C15.26 22.24 16.28 21.41 16.5 20.25L17.45 15.16C17.58 14.46 17.39 13.74 16.93 13.19ZM2.96 20.29V14.97H4.64V20.29H2.96ZM14.59 19.89C14.54 20.13 14.32 20.28 14.08 20.29H6.59V14.41L8.53 12.52C9.18 11.89 9.44 10.5 9.53 9.33996C9.7 9.40996 9.86 9.50996 9.86 9.63996V13.28C9.86 13.81 10.3 14.25 10.83 14.25H15.03C15.19 14.25 15.33 14.32 15.44 14.44C15.52 14.54 15.56 14.68 15.53 14.81L14.59 19.89Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_6398_35075">
          <rect width="21.81" height="20.24" fill="white" transform="translate(1 2)" />
        </clipPath>
      </defs>
    </svg>
  );
};
