import React, { useMemo } from "react";
import styled from "styled-components";
import { colors, H3, H7, H8, sizes, SmallLabel } from "@components/CubeBaseElement";
import { extractScore } from "@utils/extractScore";
import moment from "moment";
import { ConversationData } from "@types/ecoach-custom";
import ChevronRightIcon from "@assets/icons/ChevronRightIcon";
import { device } from "@styles/media";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@store/hooks";
import useWindowResize from "@hooks/useWindowResize";

const Card = styled.div`
  width: 100%;
  background-color: ${colors.fwdDarkGreen[100]};
  border-radius: ${sizes[3]}px;
  border: 2px solid #8b8e8f;
  height: 70px;
  padding: ${sizes[3]}px ${sizes[4]}px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: ${sizes[4]}px;
  @media ${device.noMobile} {
    height: auto;
    margin-bottom: 0;
  }
`;

const SSColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: ${sizes[4]}px;
`;

const TitleColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-right: ${sizes[4]}px;
  flex-grow: 1;
  @media ${device.mobile} {
    margin-left: ${sizes[0]}px;
    margin-right: ${sizes[0]}px;
  }
`;

const NumberColumn = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: ${sizes[4]}px;
  margin-right: ${sizes[4]}px;
  @media ${device.mobile} {
    margin-right: ${sizes[1]}px;
    margin-left: ${sizes[1]}px;
  }
`;

const CenteredText = styled(H7)`
  text-align: center;
`;

const CurrentScore = styled(H3)`
  @media (max-width: 414px) {
    font-size: 28px;
  }
`;

const TotalScore = styled.span`
  @media (max-width: 414px) {
    font-size: 0.85rem;
  }
`;

type SessionCardProps = {
  session: ConversationData;
  sessionNumber: number | "";
};

const SessionQuickFireCard = ({ session, sessionNumber }: SessionCardProps) => {
  const { width } = useWindowResize();
  const desktop = width > 1024;
  const router = useRouter();
  const { conversation_id, datetime, difficulty, product_selection, report } = session;
  const score = extractScore(report?.overall_score);
  const {
    ecoachReducer: { productConfig },
  } = useAppSelector((state) => state);

  const productName = useMemo(() => {
    const productForCurrentCountry = productConfig?.["vn"];
    return (
      productForCurrentCountry?.find((e) => e.product_code === product_selection)?.product_name ||
      product_selection
        ?.split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  }, [product_selection, productConfig]);

  const onPress = () => {
    router.push(`/ecoach/report?conversationId=${conversation_id}&ref=history`);
  };

  return (
    <Card onClick={onPress}>
      <SSColumn>
        <H8 fontWeight="bold" color={colors.white}>
          {desktop ? "Phiên làm việc" : "Phiên"} {sessionNumber}
        </H8>
        <SmallLabel fontWeight="normal" color={colors.fwdGrey[50]}>
          {moment(datetime).format("DD/MM")}
        </SmallLabel>
      </SSColumn>

      <TitleColumn>
        {productName && (
          <SmallLabel fontWeight="bold" color={colors.white}>
            {productName}
          </SmallLabel>
        )}
        <SmallLabel fontWeight="normal" color={colors.white}>
          {difficulty === "1" ? "Người Mới Bắt Đầu" : "Chuyên Gia"}
        </SmallLabel>
      </TitleColumn>

      <NumberColumn>
        <CenteredText fontWeight={"bold"} color={colors.white}>
          <CurrentScore fontWeight={"bold"} color={colors.white}>
            {score}{" "}
          </CurrentScore>
          <TotalScore>/ 100</TotalScore>
        </CenteredText>
      </NumberColumn>
      <ChevronRightIcon fill={colors.fwdOrange[100]} size={18} />
    </Card>
  );
};

export default SessionQuickFireCard;
