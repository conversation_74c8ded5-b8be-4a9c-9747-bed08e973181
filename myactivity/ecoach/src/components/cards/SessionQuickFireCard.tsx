import React from "react";
import styled from "styled-components";
import { H7, H8, colors, sizes } from "../CubeBaseElement";
import { ConversationData } from "../../types/ecoach";
import { device } from "../../styles/media";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  padding: ${sizes[4]}px;
  border-radius: ${sizes[2]}px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: ${sizes[3]}px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }

  @media ${device.noMobile} {
    flex: 1;
    margin-bottom: 0;
    margin-right: ${sizes[3]}px;
    
    &:last-child {
      margin-right: 0;
    }
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${sizes[2]}px;
`;

const SessionNumber = styled(H8)`
  color: ${colors.fwdOrange[100]};
  font-weight: bold;
`;

const Date = styled(H8)`
  color: ${colors.fwdGrey[50]};
`;

const ProductName = styled(H7)`
  color: ${colors.white};
  font-weight: bold;
  margin-bottom: ${sizes[1]}px;
`;

const Score = styled(H8)`
  color: ${colors.fwdYellow[100]};
`;

interface SessionQuickFireCardProps {
  session: ConversationData;
  sessionNumber: number;
}

const SessionQuickFireCard: React.FC<SessionQuickFireCardProps> = ({
  session,
  sessionNumber,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleClick = () => {
    // Navigate to session details
    console.log('Navigate to session:', session.conversation_id);
  };

  return (
    <Container onClick={handleClick}>
      <Header>
        <SessionNumber>Phiên #{sessionNumber}</SessionNumber>
        <Date>{formatDate(session.created_at)}</Date>
      </Header>
      <ProductName>{session.product_name || 'Sản phẩm'}</ProductName>
      <Score>Điểm: {session.overall_score || 0}/100</Score>
    </Container>
  );
};

export default SessionQuickFireCard;
