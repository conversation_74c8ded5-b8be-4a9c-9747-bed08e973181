import React from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useGetEcoachConfigurationData } from "@hooks/useGetEcoachConfigurationData";
import { device } from "@styles/media";
import { entryPointBgMobile, entryPointBgTablet } from "@assets";
import { useAppSelector } from "@store/hooks";
import { useExchangeCubeToken } from "@hooks/useExchangeCubeToken";

const Card = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  cursor: pointer;
  @media ${device.mobile} {
    padding-left: 15px;
    padding-right: 15px;
  }
`;

const BG = styled.div`
  width: 100%;
  height: 200px;
  border-radius: 16px;
  object-fit: cover;
  background-image: url(${entryPointBgTablet.src});
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  @media ${device.mobile} {
    background-image: url(${entryPointBgMobile.src});
    padding-left: 15px;
    padding-right: 15px;
  }
`;

const TGEntryPointCard = () => {
  const navigate = useNavigate();
  const {
    rootReducer: {
      user: { channel },
    },
    ecoachReducer: { isTrainerGuruEnabled },
  } = useAppSelector((state) => state);

  const { cubeToken } = useExchangeCubeToken();
  const ecoachConfig = useGetEcoachConfigurationData(!!cubeToken);

  if (!cubeToken || !ecoachConfig) {
    return null;
  }

  // Allow TG card show for both AGENCY + BANCA channel
  if (!(channel === "BANCA" || channel === "AGENCY")) {
    return null;
  }

  if (!isTrainerGuruEnabled) {
    return null;
  }

  console.log("ecoachConfig", ecoachConfig);
  return (
    <Card onClick={() => navigate(`/`)}>
      <BG />
    </Card>
  );
};

export default TGEntryPointCard;
