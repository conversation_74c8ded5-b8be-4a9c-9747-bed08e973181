import React from 'react';
import styled from 'styled-components';

const FieldsetContainer = styled.fieldset`
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin: 8px 0;
  
  legend {
    padding: 0 8px;
    font-weight: 600;
    color: #333;
  }
`;

const InputContainer = styled.div`
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const Label = styled.label`
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #555;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
`;

interface InputFieldsetProps {
  legend?: string;
  children?: React.ReactNode;
  className?: string;
}

interface InputFieldProps {
  label?: string;
  type?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
}

export const InputField: React.FC<InputFieldProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required
}) => {
  return (
    <InputContainer>
      {label && <Label>{label}</Label>}
      <Input
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
      />
    </InputContainer>
  );
};

const InputFieldset: React.FC<InputFieldsetProps> = ({ legend, children, className }) => {
  return (
    <FieldsetContainer className={className}>
      {legend && <legend>{legend}</legend>}
      {children}
    </FieldsetContainer>
  );
};

export default InputFieldset;
