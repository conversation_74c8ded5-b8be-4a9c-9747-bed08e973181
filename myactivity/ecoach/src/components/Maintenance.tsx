import React from "react";
import styled from "styled-components";
import { H3, H6, colors, sizes } from "./CubeBaseElement";

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );
  padding: ${sizes[4]}px;
`;

const Title = styled(H3)`
  color: ${colors.white};
  text-align: center;
  margin-bottom: ${sizes[4]}px;
`;

const Message = styled(H6)`
  color: ${colors.fwdGrey[50]};
  text-align: center;
  max-width: 400px;
`;

const MaintenancePage: React.FC = () => {
  return (
    <Container>
      <Title>Hệ thống đang bảo trì</Title>
      <Message>
        Chúng tôi đang nâng cấp hệ thống để mang đến trải nghiệm tốt hơn. 
        Vui lòng quay lại sau ít phút.
      </Message>
    </Container>
  );
};

export default MaintenancePage;
