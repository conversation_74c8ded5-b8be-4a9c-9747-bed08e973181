import React from "react";
import styled from "styled-components";
import { colors, H6, LargeBody, sizes } from "./CubeBaseElement";
import MaintenanceInProgressIcon from "./icons/MaintenanceInProgressIcon";
import FullScreenPage from "./FullScreenPage";
import ModalFullPage from "@components/modal-full-page";
import { useNavigate } from "react-router-dom";

const HomeTitleImg = styled.img<{ paddingTop?: number }>`
  width: 90px;
  height: 46px;
  margin: -${sizes[4]}px 0 0;
`;

const Container = styled.div`
  width: 100%;
  height: calc(100vh - 56px);
  display: flex;
  flex: 1;
  background-color: ${colors.white};
`;

const PageContainer = styled.div`
  flex: 1;
  width: 100%;
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;
`;

const UnderMaintenanceText = styled(H6)`
  text-align: center;
  max-width: 320px;
`;

const ComeBackInAFewMinutesText = styled(LargeBody)`
  padding-top: ${sizes[1]}px;
  padding-bottom: ${sizes[12]}px;
`;

const MaintenancePage = () => {
  const navigate = useNavigate();

  return (
    <ModalFullPage show={true} title="" onClose={() => navigate(-1)}>
      <Container>
        <PageContainer>
          <UnderMaintenanceText fontWeight={"bold"} color={colors.fwdDarkGreen[100]}>
            Training Guru đang được bảo trì.
          </UnderMaintenanceText>
          <ComeBackInAFewMinutesText color={colors.fwdOrange["100"]}>
            Vui lòng quay lại sau vài phút.
          </ComeBackInAFewMinutesText>
          <MaintenanceInProgressIcon />
        </PageContainer>
      </Container>
    </ModalFullPage>
  );
};

export default MaintenancePage;
