import { ButtonPrimary, ButtonSecondary } from "@styles/buttons";
import React, { useState } from "react";
import styled from "styled-components";
import { colors, H2, sizes } from "../CubeBaseElement";
import Modal from "./BaseModal";
import { device } from "@styles/media";
import { TextAlign } from "../../types/custom-types";

type ModalProps = {
  reTake: () => void;
  onExitRole: () => void;
};

const MissionFailModal: React.FC<ModalProps> = ({ reTake, onExitRole }) => {
  const [modalVisible, setModalVisible] = useState(true);

  const tryAgain = () => {
    setModalVisible(false);
    reTake();
  };

  return (
    <Modal show={modalVisible} size="ssm" title="" titleAlign={TextAlign.CENTER}>
      <Container>
        <CenterView>
          <H2 fontWeight="bold" color={"#F60000"} style={{ textAlign: "center" }}>
            Nhiệm vụ đã kết thúc
          </H2>
          <Spacer height={sizes[12]} />
          <ButtonSecondary onClick={tryAgain}>Thử lại</ButtonSecondary>
          <Spacer height={sizes[4]} />
          <ButtonPrimary onClick={onExitRole}>Thoát khỏi vai trò nhập vai</ButtonPrimary>
        </CenterView>
      </Container>
    </Modal>
  );
};

const CenterView = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  //width: windowWidth - sizes[4] * 2,
  height: 400px;
  max-width: 380px;
  border-radius: ${sizes[4]}px;
  padding: ${sizes[6]}px;
  border: 5px solid ${colors.alertRed};
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  //background-color: rgba(24, 48, 40, 0.95);
  border-radius: ${sizes[4]}px;
  overflow: hidden;
  justify-content: center;
  align-items: center;

  @media ${device.mobile} {
    margin-left: ${sizes[1]}px;
    margin-right: ${sizes[1]}px;
  }
`;

const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;

export { MissionFailModal };
