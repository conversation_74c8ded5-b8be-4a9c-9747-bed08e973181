import { TextAlign } from "@custom-types";
import Icons from "@components/icons";
import Portal from "@components/portal";
import useClickAway from "@hooks/use-click-away";
import { isNil } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import {
  Background,
  Body,
  BodyWrapper,
  ButtonClose,
  ContentModel,
  ContentWrapper,
  ModalWrapper,
  Title,
} from "./styled";

export type ModalSize = "full" | "slg" | "lg" | "md" | "sm" | "ssm";
interface ModalProp {
  show: boolean;
  size?: ModalSize;
  title?: string;
  titleAlign?: TextAlign;
  showClose?: boolean;
  borderHeader?: boolean;
  backgroundMobile?: string;
  children: React.ReactNode;
  isFooterSticky?: boolean;
  zIndex?: number;
  onClose?: () => void;
}

const Modal = ({
  show,
  size = "md",
  title,
  titleAlign,
  isFooterSticky,
  borderHeader,
  showClose = true,
  backgroundMobile,
  children,
  zIndex = 1100,
  onClose,
}: ModalProp) => {
  const [marginTop, setMarginTop] = useState(null);

  const ref = useRef<HTMLDivElement>();

  useClickAway(ref, () => {
    onClose?.();
  });

  useEffect(() => {
    if (show) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
  }, [show]);

  useEffect(() => {
    if (show) {
      const listener = () => {
        const top = Math.max((window.innerHeight - ref.current?.offsetHeight) / 2, window.innerWidth > 768 ? 0 : 0);
        setMarginTop(top);
      };

      const handleCloseModal = (e: any) => {
        if (e.key === "Escape" && showClose) {
          onClose();
        }
      };

      window.addEventListener("resize", listener);
      window.addEventListener("keydown", handleCloseModal);

      var ro = new ResizeObserver((entries) => {
        listener();
      });

      // Observe one or multiple elements
      ro.observe(ref.current);

      return () => {
        window.removeEventListener("resize", listener);
        window.removeEventListener("keydown", handleCloseModal);
        ro.disconnect();
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show, showClose]);

  if (!show) {
    return null;
  }

  return (
    <Portal>
      <ModalWrapper open={show && !isNil(marginTop)} zIndex={zIndex}>
        <ContentModel>
          <Background />
          <BodyWrapper backgroundMobile={backgroundMobile} size={size} style={{ marginTop: marginTop }}>
            <Body ref={ref} backgroundMobile={backgroundMobile}>
              {/* <ScrollBar height="auto"> */}
              <ContentWrapper isFooterSticky={isFooterSticky}>{children}</ContentWrapper>
              {/* </ScrollBar> */}
            </Body>
          </BodyWrapper>
        </ContentModel>
      </ModalWrapper>
    </Portal>
  );
};

export default Modal;
