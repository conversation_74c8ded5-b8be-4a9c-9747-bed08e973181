import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import { colors, H8, sizes } from "../CubeBaseElement";
import useWindowResize from "@hooks/use-window-resize";
import Icons from "../icons/TimeTimer";

type WelcomeTooltipModalProps = {
  avatarName: string;
  autoDismissed?: boolean;
  onClose: () => void;
};

const Container = styled.div<{ windowHeight: number }>`
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80px;
  position: absolute;
  top: ${({ windowHeight }) => `${windowHeight - 300}px`};
  display: flex;
`;

const CenterView = styled.div`
  display: flex;
  flex-direction: row;
  background-color: white;
  justify-content: flex-start;
  border-radius: ${sizes[3]}px;
  padding: ${sizes[3]}px ${sizes[5]}px;
  margin-left: ${sizes[4]}px;
  margin-right: ${sizes[4]}px;
  max-width: 440px;
`;

const ContentView = styled.div`
  margin-left: ${sizes[4]}px;
  display: flex;
  flex-direction: column;
`;

const WelcomeTooltipModal = ({ avatarName, onClose, autoDismissed = true }: WelcomeTooltipModalProps) => {
  const [modalVisible, setModalVisible] = useState(true);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { height } = useWindowResize();

  // Effect for auto-closing the modal
  useEffect(() => {
    if (modalVisible && autoDismissed) {
      // Set a timer to close the modal after 3 seconds
      timerRef.current = setTimeout(() => {
        setModalVisible(false);
        onClose?.();
      }, 10000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [modalVisible, autoDismissed]);

  return (
    <Container windowHeight={height}>
      <CenterView>
        <Icons.TimeTimer size={64} />
        <ContentView>
          <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
            Hãy thử trả lời
          </H8>
          <H8 fontWeight="medium" color={colors.fwdDarkGreen[100]}>
            {`câu hỏi của ${avatarName} về sản phẩm!`}
          </H8>
        </ContentView>
      </CenterView>
    </Container>
  );
};

export default WelcomeTooltipModal;
