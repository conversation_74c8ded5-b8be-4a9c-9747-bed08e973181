import { TextAlign } from "../../types/custom-types";
import styled from "styled-components";
import { ButtonIcon } from "@styles/buttons";
import { FadeReverseStyled, FadeStyled } from "@styles/fade-animation";
import { device } from "@styles/media";
import { ModalSize } from "./BaseModal";
import { ScrollBarStyle } from "../../styles";

export const ModalWrapper = styled.div<{ open: boolean; zIndex: number }>`
  width: 100vw;
  height: calc(100 * var(--vh));

  position: fixed;
  top: 0;
  left: 0;
  z-index: ${({ zIndex }) => zIndex};

  ${({ open }) => (open ? FadeStyled : FadeReverseStyled)}

  background-color: rgba(24, 48, 40, 0.90);
`;

export const ContentModel = styled.div`
  width: 100%;
  height: 100%;

  padding-bottom: 16px;
  padding-top: 16px;

  display: flex;
  justify-content: center;

  position: relative;

  @media ${device.mobile} {
    padding: 0px;
  }
`;

export const Background = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;

  opacity: 0.5;
`;

export const BodyWrapper = styled.div<{ size?: ModalSize; backgroundMobile: string }>`
  width: ${({ size }) =>
    size == "full"
      ? "calc(100% - 30px)"
      : size === "slg"
      ? "1240px"
      : size == "lg"
      ? "1024px"
      : size == "md"
      ? "572px"
      : size == "ssm"
      ? "380px"
      : "500px"};
  height: 100%;

  overflow: auto;
  border-radius: 16px;
  z-index: 1100;

  ${ScrollBarStyle} @media ${device.mobile} {
    width: 100%;
    height: calc(100 * var(--vh));
    margin: 0px !important;

    border-radius: 0px;
    padding: 0px;
  }
`;

export const Body = styled.div<{ backgroundMobile: string }>`
  width: 100%;
  height: 100%;
  padding: 0;

  position: relative;

  border-radius: 16px;
`;

export const Title = styled.h6<{ textAlign: TextAlign; borderHeader?: boolean }>`
  margin-bottom: 24px;
  text-align: ${({ textAlign }) => textAlign ?? "left"};

  @media ${device.mobile} {
    width: 100%;
    padding: 12px 16px;
    padding-left: 44px;

    position: fixed;
    z-index: 10;
    top: 0;

    font-size: 16px;

    border-bottom: 1px solid ${({ theme, borderHeader }) => (borderHeader ? theme.color.status.grey : "transparent")};
  }
`;

export const ButtonClose = styled(ButtonIcon)<{ show?: boolean }>`
  display: ${({ show }) => (show ? "block" : "none")};

  position: absolute;
  top: 24px;
  right: 24px;

  @media ${device.mobile} {
    display: ${({ show }) => (show ? "block" : "none")};

    z-index: 1;
    top: 12px;
    left: 16px;

    svg {
      width: 20px;
      height: 20px;
    }
  }
`;

export const ContentWrapper = styled.div<{ isFooterSticky: boolean }>`
  width: 100%;
  height: 100%;
  display: flex;
  @media ${device.mobile} {
    padding-top: 69px;
    padding-bottom: ${({ isFooterSticky }) => (isFooterSticky ? "100px" : "24px")};
    height: calc(100% - 69px - ${({ isFooterSticky }) => (isFooterSticky ? "100px" : "24px")});
  }
`;

export const ModalFooter = styled.div`
  width: 100%;
  margin-top: 24px;

  display: flex;
  justify-content: center;

  @media ${device.mobile} {
    padding: 16px;

    position: fixed;
    left: 0px;
    bottom: 0px;
    z-index: 10;

    border-top: 1px solid ${({ theme }) => theme.color.status.grey};
  }
`;
