import { ValueLabel } from "@custom-types";
import { ChannelType } from "./user";

export interface ChartDataProps {
  title: string;
  date: string;
  fyp: number;
  ape: number;
  fypMax: number;
  apeMax: number;
}

export type PerformanceType = "daily" | "monthly" | "yearly" | "quarterly" | "mtd" | "ytd" | "";

export interface PerformanceFormSearchValue {
  error: string;
  type: PerformanceType;
  date: string;
  quarterly?: ValueLabel;
}

export interface PerformanceChannelProps {
  dataDetail: any;
  dataChartOverview: ChartDataProps[];
  dataChartSales?: any;
  rank: RankOutput;
}

interface ChildrenInfoContract {
  title: string;
  children: ValueLabel[];
}

export interface DataContract {
  children: ChildrenInfoContract[];
}

export interface LineChartProps {
  xAxis: any[];
  data: LineChartData[];
}

export interface LineChartData {
  name?: string;
  color: string;
  data: number[];
  total?: number;
}

//API

/** Performance personal */

export interface PerformanceKPIInput {
  agentId: string;
  channelCd: string;
}

export interface PerformanceKPIOutput {
  agentCode: string;
  year: number;
  month: number;
  day: number;
  fyPDaily: number;
  apeDaily: number;
  dailyTargetFyp: number;
  dailyTargetApe: number;
  fypMonthly: number;
  apeMonthly: number;
  mtdTargetFyp: number;
  mtdTargetApe: number;
  fypYearly: number;
  apeYearly: number;
  ytdTargetFyp: number;
  ytdTargetApe: number;
}

/** Performance detail */
export interface PerformanceDetailBody {
  body: PerformanceDetailInput;
  type: PerformanceType;
}

export interface PerformanceDetailInput {
  agentId: string;
  channelCd: string;
  day: string;
  designationCd?: string;
  month: string;
  year: string;
}

export interface PerformanceDetailOutput<T> {
  agentId: string;
  agentName: string;
  channelCd: string;
  data: T;
}

export interface PerformanceDetailDaily {
  IndividualDailyRyp: number;
  IndividualDailyIssueCaseCount: number;
  IndividualDailySyp: number;
  IndividualDailyIssueIp: number;
  IndividualDailySubmitIp: number;
  IndividualDailyPendingCaseCount: number;
  IndividualDailyPendingApe: number;
  IndividualDailyNetIssueApe: number;
  IndividualDailyIssueApe: number;
  IndividualDailyNetIssueCaseCount: number;
  IndividualDailyPendingIp: number;
  IndividualDailyCancelIp: number;
  IndividualDailyFyp: number;
  IndividualDailySubmitCaseCount: number;
  IndividualDailyCancelCaseCount: number;
  IndividualDailySubmitApe: number;
  IndividualDailyNetIssueIp: number;
  IndividualDailyCancelApe: number;
  dulcancelApe: number;
  dulcancelCaseCount: number;
  dulcancelIp: number;
  dulissueApe: number;
  dulissueCaseCount: number;
  dulissueIp: number;
  dulpendingApe: number;
  dulpendingCaseCount: number;
  dulpendingIp: number;
  dulsubmitApe: number;
  dulsubmitCaseCount: number;
  dulsubmitIp: number;
  fypOnline: number;
  onlineCancelApe: number;
  onlineCancelCaseCount: number;
  onlineCancelIp: number;
  onlineIssueApe: number;
  onlineIssueCaseCount: number;
  onlineIssueIp: number;
  onlinePendingApe: number;
  onlinePendingCaseCount: number;
  onlinePendingIp: number;
  onlineSubmitApe: number;
  onlineSubmitCaseCount: number;
  onlineSubmitIp: number;
}

export interface PerformanceDetailMonthly {
  IndividualMonthlyIssueCaseCount: number;
  IndividualMonthlyRyc: number;
  IndividualMonthlyFyp: number;
  IndividualMonthlySyc: number;
  IndividualMonthlyPendingPrevMonthIp: number;
  IndividualMonthlyPendingApe: number;
  IndividualMonthlyAdjustmentCaseCount: number;
  IndividualMonthlyPendingIp: number;
  IndividualMonthlyPendingPrevMonthCaseCount: number;
  IndividualMonthlySubmitIp: number;
  IndividualMonthlyIssueIp: number;
  IndividualMonthlyAdjustmentApe: number;
  IndividualMonthlySubmitCaseCount: number;
  IndividualMonthlyCancelIp: number;
  IndividualMonthlyNetIssueCaseCount: number;
  IndividualMonthlyRyp: number;
  IndividualMonthlySyp: number;
  IndividualMonthlyPendingPrevMonthApe: number;
  IndividualMonthlyPendingCaseCount: number;
  IndividualMonthlyNetIssueApe: number;
  IndividualMonthlyIssueApe: number;
  IndividualMonthlySubmitApe: number;
  IndividualMonthlyCancelApe: number;
  IndividualMonthlyNetIssueIp: number;
  IndividualMonthlyFyc: number;
  IndividualMonthlyAdjustmentIp: number;
  IndividualMonthlyCancelCaseCount: number;
  dulcancelApe: number;
  dulcancelCaseCount: number;
  dulcancelIp: number;
  duladjApe: number;
  duladjCaseCount: number;
  duladjIp: number;
  dulissueApe: number;
  dulissueCaseCount: number;
  dulissueIp: number;
  dulnetIssueApe: number;
  dulnetIssueCaseCount: number;
  dulnetIssueIp: number;
  dulpendingApe: number;
  dulpendingCaseCount: number;
  dulpendingIp: number;
  dulsubmitApe: number;
  dulsubmitCaseCount: number;
  dulsubmitIp: number;
  fypOnline: number;
  onlineAdjApe: number;
  onlineAdjCaseCount: number;
  onlineAdjIp: number;
  onlineCancelApe: number;
  onlineCancelCaseCount: number;
  onlineCancelIp: number;
  onlineIssueApe: number;
  onlineIssueCaseCount: number;
  onlineIssueIp: number;
  onlineNetIssueApe: number;
  onlineNetIssueCaseCount: number;
  onlineNetIssueIP: number;
  onlinePendingApe: number;
  onlinePendingCaseCount: number;
  onlinePendingIp: number;
  onlineSubmitApe: number;
  onlineSubmitCaseCount: number;
  onlineSubmitIp: number;
}

export interface PerformanceDetailYearly {
  IndividualYearlyNetIssueApe: number;
  IndividualYearlyNetIssueCaseCount: number;
  IndividualYearlyNetIssueIp: number;
  IndividualYearlySyp: number;
  IndividualYearlyRyp: number;
  IndividualYearlyIssueCaseCount: number;
  IndividualYearlyCancelCaseCount: number;
  IndividualYearlyIssueIp: number;
  IndividualYearlyCancelIp: number;
  IndividualYearlyIssueApe: number;
  IndividualYearlySubmitApe: number;
  IndividualYearlySubmitCaseCount: number;
  IndividualYearlyCancelApe: number;
  IndividualYearlySubmitIp: number;
  IndividualYearlyFyp: number;
  IndividualYearlyFyc: number;
  IndividualYearlySyc: number;
  IndividualYearlyRyc: number;
  dulnetIssueApe: number;
  dulnetIssueCaseCount: number;
  dulnetIssueIp: number;
  dulsubmitApe: number;
  dulsubmitCaseCount: number;
  dulsubmitIp: number;
  fypOnline: number;
  onlineNetIssueApe: number;
  onlineNetIssueCaseCount: number;
  onlineNetIssueIP: number;
  onlineSubmitApe: number;
  onlineSubmitCaseCount: number;
  onlineSubmitIp: number;
}

export interface PerformanceDetailQuarterly {
  IndividualQuarterlyFyp: number;
  IndividualQuarterlyIssueApe: number;
  IndividualQuarterlyIssueCaseCount: number;
  IndividualQuarterlyIssueIp: number;
  IndividualQuarterlyNetIssueApe: number;
  IndividualQuarterlyNetIssueCaseCount: number;
  IndividualQuarterlyNetIssueIp: number;
  IndividualQuarterlySubmitApe: number;
  IndividualQuarterlySubmitCaseCount: number;
  IndividualQuarterlySubmitIp: number;
}

/** Sales Personal */

export interface SalesPersonalInput {
  agentId: string;
  channelCd: ChannelType;
  designationCd: string;
  month: string;
  year: string;
}

export interface SalesPersonalOutput {
  yyyy: number;
  mm: number;
  agentCode: string;
  agentName: string;
  designationCd: string;
  channelCd: string;
}

export interface SalesSubmittedData extends SalesPersonalOutput {
  submit_cc: number;
  submit_ip: number;
  submit_ape: number;
}

export interface SalesNetIssueData extends SalesPersonalOutput {
  netissue_cc: number;
  netissue_ip: number;
  netissue_ape: number;
}

export interface SalesActualFeeData extends SalesPersonalOutput {
  fyp: number;
  syp: number;
  ryp: number;
}

export interface SalesCommissionData extends SalesPersonalOutput {
  fyc: number;
  syc: number;
  ryc: number;
}

export interface SalesOutput<T> {
  data: T[];
}

/** Only for BANCA */

export interface DesignationLevelOutput {
  subChannelId: string;
  listParent: {
    parentCode: string;
    parentName: string;
    parentDesignationCd: string;
    levelParent: number;
  }[];
}

export interface FYPbyMonthOutput {
  listData: FYPbyMonthData[];
}

export interface FYPbyMonthData {
  agentCode: string;
  mmyyyy: string;
  fyp: number;
}

export interface SalesByBranchOutput {
  listData: SalesByBranchData[];
}

export interface SalesByBranchData {
  agentCode: string;
  branchCode: string;
  branchName: string;
  ape: number;
  fyp: number;
  caseCount: number;
  caseSize: number;
  leadCount: number;
}

export interface RankInput {
  agentId: string;
  channelCd: string;
}

export interface RankOutput {
  agentCode: string;
  askOfDate: string;
  agentCount: number;
  fypRanking: number;
  fypCompletion: number;
  fypProtectionRanking: number;
  caseRanking: number;
  caseCompletion: number;
  fypSubmission: number;
  agentTeam: number;
  idxInTeam: number;
  maxFypCompletion: number;
  agentName?: string; //get data from agentDetailModal
}

export interface AchievementOutput {
  mdrt: MdrtData;
  elite: EliteData;
}

export type MDRTType = "Elite" | "MDRT" | "COT" | "TOT" | "";

export interface MdrtData {
  agentCode: string;
  shortfallToCOT: number;
  shortfallToCOTPercent: number;
  shortfallToMDRT: number;
  shortfallToMDRTPercent: number;
  shortfallToNextTier: MDRTType;
  shortfallToNextTierValue: number;
  shortfallToNextTierValuePercent: number;
  shortfallToTOT: number;
  shortfallToTOTPercent: number;
  tierLevelAchievement: MDRTType;
  total: number;
  fyp: number;
  totlevel: number;
  mdrtlevel: number;
  cotlevel: number;
  tot: number;
  cot: number;
  mdrt: number;
}

export interface EliteData {
  elitePercent: number;
  eliteFypTarget: number;
  eliteMissingPart: number;
  fyp: number;
}

//CUBE-733
export interface GetBoListTeamsInput {
  agentId: string;
  year: string;
  month: string;
}

export interface GetBoListTeamsOutput {
  data: BoListData[];
}

export interface BoListData {
  boCode: string;
  boName: string;
}

export interface GetToListTeamsInput {
  agentId: string;
  year: string;
  month: string;
  boCode: string; //null => get all
}

export interface GetToListTeamsOutput {
  data: ToListData[];
}

export interface ToListData {
  oCode: string;
  toName: string;
  toCode: string;
}

export interface DetailTableSalesByBranchInput {
  boCode: string;
  toCode: string;
  year: string;
  month: string;
  day?: string;
  agentId: string;
}

export interface DetailTableSalesByBranch {
  data: DetailTableSalesByBranchData[];
}

export interface DetailTableSalesByBranchData {
  boCode: string;
  boName: string;
  toCode: string;
  toName: string;
  fscCode: string;
  fscName: string;
  subChannelId: string;
  policyNumber: string;
  planCode: string;
  prdtName: string;
  fyp: number;
}
