import { IconProperty } from "@custom-types";
import React from "react";

const IconAgencyGroup = ({ width = 36, height = 36, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M29.9446 5.37192C26.7061 2.37192 22.4408 0.792969 18.0175 0.792969C12.8833 0.792969 7.98609 3.00349 4.58963 6.79297C-2.04532 14.214 -1.41342 25.6614 6.0114 32.214C9.32888 35.1351 13.5152 36.793 17.9385 36.793C23.0727 36.793 27.9699 34.5824 31.4454 30.793C38.0803 23.3719 37.3695 11.9245 29.9446 5.37192Z"
        fill="white"
      />
      <g clipPath="url(#clip0_2520_151344)">
        <path
          d="M15.4585 16.8811C15.3765 16.8542 15.2945 16.8542 15.2126 16.8003L12.4251 15.804H8.84514C8.2166 16.0463 6.65891 16.6387 6.19433 16.8003C6.11235 16.8272 6.00304 16.8542 5.92105 16.8811C5.07389 17.0965 4.5 17.8236 4.5 18.6853V19.897C4.5 20.7318 5.1832 21.405 6.03036 21.405H15.3765C16.2237 21.405 16.9069 20.7318 16.9069 19.897V18.6853C16.8796 17.8236 16.3057 17.0965 15.4585 16.8811Z"
          fill="#F3BB91"
        />
        <path
          d="M10.9762 16.2079H10.2384C8.46205 16.2079 7.01367 14.7807 7.01367 13.0303V11.8186C7.01367 10.0952 8.46205 8.66797 10.2384 8.66797H10.9762C12.7525 8.66797 14.2009 10.0952 14.2009 11.8455V13.0573C14.1736 14.8076 12.7525 16.2079 10.9762 16.2079Z"
          fill="#F3BB91"
        />
        <path
          d="M30.0777 16.8811C29.9957 16.8542 29.9137 16.8542 29.8317 16.8003L27.0443 15.804H23.4643C22.8358 16.0463 21.2781 16.6387 20.8135 16.8003C20.7315 16.8272 20.6222 16.8542 20.5402 16.8811C19.7204 17.0696 19.1465 17.7966 19.1465 18.6583V19.8701C19.1465 20.7049 19.8297 21.3781 20.6768 21.3781H30.023C30.8702 21.3781 31.5534 20.7049 31.5534 19.8701V18.6583C31.4987 17.8236 30.8975 17.0965 30.0777 16.8811Z"
          fill="#F3BB91"
        />
        <path
          d="M25.57 16.2079H24.8321C23.0558 16.2079 21.6074 14.7807 21.6074 13.0303V11.8186C21.6347 10.0952 23.0831 8.66797 24.8321 8.66797H25.57C27.3463 8.66797 28.7947 10.0952 28.7947 11.8455V13.0573C28.7947 14.8076 27.3463 16.2079 25.57 16.2079Z"
          fill="#F3BB91"
        />
        <path
          d="M23.8214 23.4516C23.7121 23.4247 23.6301 23.3978 23.5208 23.3708L20.1321 22.1591H15.7596C14.9945 22.4553 13.1088 23.1554 12.5349 23.3708C12.4256 23.4247 12.3163 23.4516 12.1797 23.4785C11.1959 23.7209 10.4854 24.6095 10.4854 25.6328V27.1138C10.4854 28.1102 11.3052 28.945 12.3437 28.945H23.7121C24.7232 28.945 25.5704 28.1371 25.5704 27.1138V25.6328C25.543 24.5826 24.8052 23.694 23.8214 23.4516Z"
          fill={fill}
        />
        <path
          d="M17.89 22.6438C15.4852 22.6438 13.5176 20.7049 13.5176 18.3353V17.7428C13.5176 15.3732 15.4852 13.4343 17.89 13.4343C20.2949 13.4343 22.2625 15.3732 22.2625 17.7428V18.3353C22.2352 20.7049 20.2949 22.6438 17.89 22.6438Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_2520_151344">
          <rect width="36" height="36" fill="white" transform="translate(4.5 8.66797)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconAgencyGroup;
