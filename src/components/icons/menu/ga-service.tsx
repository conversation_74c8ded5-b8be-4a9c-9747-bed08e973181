import { IconProperty } from "@custom-types";
import React from "react";

const GAService = ({ width = 24, height = 24, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.38 20.6794C12.175 20.9394 11.78 20.9344 11.585 20.6694L11.19 20.1344C11.145 20.0794 9.94 18.5444 7 18.5444C5.215 18.5444 4.30003 18.6744 3.84503 18.7794C3.25503 18.9194 2.63501 18.6994 2.26501 18.1744C2.08001 17.9194 2 17.6044 2 17.2894V4.66937L2.23999 4.3894C2.42999 4.1694 3.01 3.49939 7 3.49939C9.515 3.49939 11.135 4.43435 12.01 5.14935C12.9 4.43935 14.54 3.49939 17 3.49939C19.505 3.49939 21.13 3.7844 21.69 4.3194L22 4.61438V17.2944C22 17.6094 21.915 17.9244 21.735 18.1794C21.36 18.6994 20.745 18.9194 20.155 18.7794C19.7 18.6694 18.78 18.5394 17 18.5394C14.08 18.5394 12.85 20.0794 12.795 20.1444L12.38 20.6794ZM6.995 16.5494C9.505 16.5494 11.125 17.4594 12 18.1594C12.88 17.4594 14.5 16.5494 16.995 16.5494C18.22 16.5494 19.225 16.6094 19.995 16.7244V5.73438C19.53 5.62937 18.63 5.50439 16.995 5.50439C14.145 5.50439 12.835 7.09437 12.78 7.16437L11.955 8.1944L11.18 7.12939C11.135 7.06939 9.91999 5.50439 6.98999 5.50439C5.33499 5.50439 4.44499 5.62437 3.98999 5.72437V16.7194C4.75999 16.6044 5.765 16.5494 6.995 16.5494Z"
        fill={fill}
      />
      <path
        d="M13.24 9.88438L12.785 8.98435C12.65 8.71435 12.78 8.38934 13.065 8.28434C13.935 7.97434 15.86 7.48437 18.635 7.81437C18.915 7.84937 19.105 8.10938 19.065 8.38438L18.915 9.37437C18.875 9.64437 18.625 9.82435 18.355 9.79435C16.085 9.52935 14.525 9.90438 13.835 10.1344C13.605 10.2094 13.35 10.1044 13.24 9.88438Z"
        fill={fill}
      />
      <path
        d="M10.8294 9.88438L11.2843 8.98435C11.4193 8.71435 11.2894 8.38934 11.0044 8.28434C10.1344 7.97434 8.20937 7.48437 5.43437 7.81437C5.15437 7.84937 4.96438 8.10938 5.00438 8.38438L5.15434 9.37437C5.19434 9.64437 5.44434 9.82435 5.71434 9.79435C7.98434 9.52935 9.54436 9.90438 10.2344 10.1344C10.4644 10.2094 10.7194 10.1044 10.8294 9.88438Z"
        fill={fill}
      />
      <path
        d="M13.24 12.5194L12.785 11.6194C12.65 11.3494 12.78 11.0244 13.065 10.9244C13.935 10.6144 15.86 10.1244 18.635 10.4544C18.915 10.4894 19.105 10.7493 19.065 11.0243L18.915 12.0144C18.875 12.2844 18.625 12.4644 18.355 12.4344C16.085 12.1694 14.525 12.5443 13.835 12.7743C13.605 12.8443 13.35 12.7394 13.24 12.5194Z"
        fill={fill}
      />
      <path
        d="M10.8294 12.5194L11.2843 11.6194C11.4193 11.3494 11.2894 11.0244 11.0044 10.9244C10.1344 10.6144 8.20937 10.1244 5.43437 10.4544C5.15437 10.4894 4.96438 10.7493 5.00438 11.0243L5.15434 12.0144C5.19434 12.2844 5.44434 12.4644 5.71434 12.4344C7.98434 12.1694 9.54436 12.5443 10.2344 12.7743C10.4644 12.8443 10.7194 12.7394 10.8294 12.5194Z"
        fill={fill}
      />
      <path
        d="M13.24 15.5394L12.785 14.6394C12.65 14.3694 12.78 14.0444 13.065 13.9394C13.935 13.6294 15.86 13.1394 18.635 13.4694C18.915 13.5044 19.105 13.7644 19.065 14.0394L18.915 15.0294C18.875 15.2994 18.625 15.4794 18.355 15.4494C16.085 15.1844 14.525 15.5594 13.835 15.7894C13.605 15.8694 13.35 15.7594 13.24 15.5394Z"
        fill={fill}
      />
      <path
        d="M10.8294 15.5394L11.2843 14.6394C11.4193 14.3694 11.2894 14.0444 11.0044 13.9394C10.1344 13.6294 8.20937 13.1394 5.43437 13.4694C5.15437 13.5044 4.96438 13.7644 5.00438 14.0394L5.15434 15.0294C5.19434 15.2994 5.44434 15.4794 5.71434 15.4494C7.98434 15.1844 9.54436 15.5594 10.2344 15.7894C10.4644 15.8694 10.7194 15.7594 10.8294 15.5394Z"
        fill={fill}
      />
    </svg>
  );
};

export default GAService;
