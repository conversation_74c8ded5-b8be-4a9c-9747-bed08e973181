import { EMPTY_DATA, ERROR, ERROR_API_MESSAGE, WARNING } from "@constants/message";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { ErrorPolicy, PolicyInquiry, PolicyInquiryInput, PolicyOutput } from "@custom-types/policy-tracking";
import { getPolicyInquiry } from "api/policy-tracking";
import { Alert } from "components/alert";
import PolicyContent from "components/policy-content";
import StatusOfTable from "components/status-of-table";
import useActionApi from "hooks/use-action-api";
import useIsGroup from "hooks/use-is-group";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep } from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { dateTypeContractInformationList, statusContractInformationList } from "screens/policy-tracking/constants";
import { setPolicyDetail } from "screens/policy-tracking/slice";
import PolicyTable from "sections/policy-tracking/policy-table";
import FormSearchInformation from "sections/policy-tracking/quan-ly-hop-dong/thong-tin-hop-dong/form-search";
import { formatValueTable } from "services/format-value";
import { formatDateRequest, getDateFrom, getDateTo, handleCheckValidateMonthForm } from "services/untils";
import { PolicyNo } from "styles";

const ContractInformation = () => {
  const [formSearch, setFormSearch] = useState<PolicyInquiryInput>({
    agentId: "",
    advancePolicyNo: "",
    fullName: "",
    policyNo: "",
    phoneNumber: "",
    group: "",
    status: statusContractInformationList.find((item) => item.value === "IF"),
    dateType: dateTypeContractInformationList.find((item) => item.value === "REG"),
    fromDate: getDateFrom(),
    toDate: getDateTo(),
  });
  const [error, setError] = useState<ErrorPolicy>({
    fromDate: null,
    toDate: null,
    phoneNumber: null,
  });
  const [openQuickSearch, setOpenQuickSearch] = useState(false);
  const [dataPolicyInquiry, setDataPolicyInquiry] = useState<PolicyInquiry[]>([]);

  const dispatch = useDispatch();
  const isGroup = useIsGroup();

  const { username } = useAppSelector((state) => state.rootReducer.user);
  const {
    currentAgentOfTeam: { agentCode },
  } = useAppSelector((state) => state.teamPerformanceReducer);

  const actionGetPolicyInquiry = useActionApi<PolicyInquiryInput, PolicyOutput<PolicyInquiry>>(getPolicyInquiry);

  const configTable: TableConfig[] = useMemo(() => {
    return isGroup
      ? [
          { key: "agentCode", show: true, type: "string", label: "Mã TVTC" },
          { key: "agentName", show: true, type: "string", label: "Tên TVTC" },
          { key: "policyNo", show: true, type: "string", label: "Số hợp đồng" },
          { key: "fullnameOW", show: true, type: "string", label: "Bên mua BH" },
          { key: "status", show: true, type: "string", label: "Tình trạng HĐ" },
          { key: "rcdDate", show: true, type: "date", label: "Ngày hiệu lực HĐ", sort: true },
          { key: "submitDate", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "firstIssueDate", show: true, type: "date", label: "Ngày phát hành lần đầu", sort: true },
          { key: "issueDate", show: true, type: "date", label: "Ngày phát hành gần nhất", sort: true },
          { key: "qaPassDate", show: true, type: "date", label: "Ngày thông báo phát hành HĐ", sort: true },
          { key: "phoneNumber", show: true, type: "string", label: "Số điện thoại khách hàng" },
          { key: "address", show: false, type: "string", label: "Địa chỉ khách hàng" },
          { key: "dueDate", show: true, type: "date", label: "Ngày đến hạn đóng phí", sort: true },
          { key: "surrenderDate", show: false, type: "date", label: "Ngày hủy hợp đồng", sort: true },
          { key: "frequency", show: false, type: "string", label: "Định kỳ đóng phí" },
          { key: "sumAssured", show: true, type: "number", label: "Số tiền BH" },
          { key: "totalPremium", show: true, type: "number", label: "Phí BH định kỳ" },
          { key: "totalPremium", show: false, type: "number", label: "Phí BH đến hạn còn lại cần đóng" },
          { key: "prdtDesc", show: true, type: "string", label: "Sản phẩm chính" },
          { key: "fullnameLF", show: false, type: "string", label: "Người được BH" },
          { key: "dobOW", show: false, type: "date", label: "Ngày sinh BMBH", sort: true },
        ]
      : [
          { key: "policyNo", show: true, type: "string", label: "Số hợp đồng" },
          { key: "fullnameOW", show: true, type: "string", label: "Bên mua BH" },
          { key: "status", show: true, type: "string", label: "Tình trạng HĐ" },
          { key: "rcdDate", show: true, type: "date", label: "Ngày hiệu lực HĐ", sort: true },
          { key: "submitDate", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "firstIssueDate", show: true, type: "date", label: "Ngày phát hành lần đầu", sort: true },
          { key: "issueDate", show: true, type: "date", label: "Ngày phát hành gần nhất", sort: true },
          { key: "qaPassDate", show: true, type: "date", label: "Ngày thông báo phát hành HĐ", sort: true },
          { key: "phoneNumber", show: true, type: "string", label: "Số điện thoại khách hàng" },
          { key: "address", show: false, type: "string", label: "Địa chỉ khách hàng" },
          { key: "dueDate", show: true, type: "date", label: "Ngày đến hạn đóng phí", sort: true },
          { key: "surrenderDate", show: false, type: "date", label: "Ngày hủy hợp đồng", sort: true },
          { key: "frequency", show: false, type: "string", label: "Định kỳ đóng phí" },
          { key: "sumAssured", show: true, type: "number", label: "Số tiền BH" },
          { key: "totalPremium", show: true, type: "number", label: "Phí BH định kỳ" },
          { key: "totalPremium", show: false, type: "number", label: "Phí BH đến hạn còn lại cần đóng" },
          { key: "prdtDesc", show: true, type: "string", label: "Sản phẩm chính" },
          { key: "fullnameLF", show: false, type: "string", label: "Người được BH" },
          { key: "dobOW", show: false, type: "date", label: "Ngày sinh BMBH", sort: true },
        ];
  }, [isGroup]);

  const showDetailPolicy = (data: PolicyInquiry) => {
    if (isGroup) {
      return;
    }

    dispatch(
      setPolicyDetail({
        data: {
          agentId: data.agentCode,
          policyNo: data.policyNo,
        },
      })
    );
  };

  const formatValueTablePolicy = (data: any, config: TableConfig) => {
    switch (config.key) {
      case "policyNo":
        return (
          <PolicyNo primary={!isGroup} onClick={() => showDetailPolicy(data)}>
            {data[config.key]}
          </PolicyNo>
        );
      case "status":
        return <StatusOfTable status={data.statusCode} label={data[config.key]} />;
      default:
        return formatValueTable(data, config);
    }
  };

  const dataTable: DataTable[][] = useMemo(
    () =>
      dataPolicyInquiry?.map((d: any) =>
        configTable.map((config) => ({
          config: config,
          node: formatValueTablePolicy(d, config),
          originData: d[config.key],
        }))
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataPolicyInquiry, configTable]
  );

  //onChange & validate
  const disableSubmit = useMemo(() => {
    return Object.values(error).findIndex((value) => value) > -1;
  }, [error]);

  const handleChangeData = (name: string, value: string) => {
    setFormSearch((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromDate" || name === "toDate") {
      const newError = handleCheckValidateMonthForm(name, value, formSearch.fromDate, isGroup ? 3 : 12, false);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const handleBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleCheckValidateMonthForm(name, formSearch.toDate, valueDateFrom, isGroup ? 3 : 12, false);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  const handleSubmit = useCallback(() => {
    const payload = cloneDeep(formSearch);

    payload.agentId = isGroup ? agentCode : username; // agentCode get from  currentAgentOfTeam(Hierarchy tree)
    payload.dateType = formSearch.dateType.value;
    payload.fromDate = formatDateRequest(formSearch.fromDate);
    payload.toDate = formatDateRequest(formSearch.toDate);
    payload.status = formSearch.status.value;
    payload.group = isGroup ? "1" : "";

    actionGetPolicyInquiry({
      body: payload,
      loading: {
        type: "local",
        name: "getPolicyTrackingLoading",
      },
    })
      .then(({ data }) => {
        setDataPolicyInquiry(data.listData);

        if (!data.listData.length) {
          Alert(WARNING, EMPTY_DATA);
        }
      })
      .catch((error) => {
        console.log("error", error);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formSearch, username, isGroup, agentCode]);

  const handleOpenQuickSearch = (value: boolean) => {
    setOpenQuickSearch(value);
  };

  return (
    <PolicyContent
      title="Thông tin hợp đồng"
      openQuickSearch={openQuickSearch}
      onOpenQuickSearch={handleOpenQuickSearch}
    >
      <FormSearchInformation
        showHierarchy={isGroup}
        disableSubmit={disableSubmit}
        error={error}
        onBlurData={handleBlurData}
        formSearch={formSearch}
        onChangeData={handleChangeData}
        onSubmit={handleSubmit}
      />
      <PolicyTable data={dataTable} config={configTable} />
    </PolicyContent>
  );
};

export default ContractInformation;
