import { DataTable, TableConfig } from "@custom-types/config-table";
import { CoiRef, ConfigEreData } from "@custom-types/ere-approval";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import moment from "moment";
import { useCallback, useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { FormatCoiQuest } from ".";
import { getLabelOfList } from "../../utils";
import { Answer, ImageItem, ImgSectionWrapper, SubQuestion } from "./styled";

//Lợi ích liên kết kinh doanh
const configTableBusiness: TableConfig[] = [
  { label: "Tên Công ty/Doanh nghiệp", key: "companyName", show: true },
  { label: "Nhóm công ty", key: "companyGroup", show: true },
  { label: "Tên của thành viên gia đình", key: "familyName", show: true },
  { label: "<PERSON><PERSON><PERSON> quan hệ", key: "familyRelationship", show: true },
  { label: "<PERSON><PERSON><PERSON> danh", key: "position", show: true },
  { label: "<PERSON><PERSON><PERSON> ban", key: "department", show: true },
  { label: "Ngày bắt đầu làm việc", key: "startingPosDate", show: true },
];

const BusinessLinkQuestionBlock = ({ coiFormData, config }: { coiFormData: FormatCoiQuest; config: ConfigEreData }) => {
  const { companyGroupList, relationshipList, businessPositionList, departmentList } = config ?? {};

  const formatValueBusiness = useCallback(
    (data: CoiRef, config: TableConfig) => {
      switch (config.key) {
        case "startingPosDate":
          return moment(data[config.key], "YYYY-MM-DD").format("DD/MM/YYYY");
        case "companyGroup":
          return getLabelOfList(companyGroupList, data[config.key]);
        case "familyRelationship":
          return getLabelOfList(relationshipList, data[config.key]);
        case "position":
          return getLabelOfList(businessPositionList, data[config.key]);
        case "department":
          return getLabelOfList(departmentList, data[config.key]);
        default:
          return formatValueTable(data, config);
      }
    },
    [businessPositionList, companyGroupList, departmentList, relationshipList]
  );

  const formatTableBusiness: DataTable[][] = useMemo(
    () =>
      coiFormData?.coiRef?.map((d: any) =>
        configTableBusiness.map((config) => ({
          config: config,
          node: formatValueBusiness(d, config),
          originData: d[config.key],
        }))
      ) ?? [],
    [coiFormData?.coiRef, formatValueBusiness]
  );

  return (
    <>
      <label className="label-1">{`${coiFormData?.questId}. ${coiFormData?.questName}`}</label>
      <SubQuestion className="body-5">
        Bạn có thành viên gia đình làm việc tại FWD Việt Nam/ thành viên trong tập đoàn FWD hoặc đang là nhân viên/ đại
        lý bảo hiểm của công ty bảo hiểm khác không?
      </SubQuestion>
      <Answer className="h7">{coiFormData?.answer === "Y" ? "Có" : "Không"}</Answer>
      {coiFormData?.coiRef.length ? (
        <>
          <Table config={configTableBusiness} data={formatTableBusiness} showConfig={false} showPagination={false} />
          <TableMobile config={configTableBusiness} data={formatTableBusiness} />
        </>
      ) : null}
      {coiFormData?.subQuestData?.length
        ? coiFormData.subQuestData?.map((item, index) => {
            return (
              <div key={index}>
                <SubQuestion className="body-5 mt-16">{`${item.questId}.${item.subQuestId} ${item.subQuestName}`}</SubQuestion>
                <Answer className="h7"> {item.answer === "Y" ? "Đồng ý" : "Không đồng ý"}</Answer>
                <label className="label-3">{item?.docName}</label>
                {item?.docData?.length ? (
                  <ImgSectionWrapper>
                    <ImageItem>
                      <img src={item.docData} alt="doc-img" />
                    </ImageItem>
                  </ImgSectionWrapper>
                ) : null}
              </div>
            );
          })
        : null}
    </>
  );
};

export default BusinessLinkQuestionBlock;
