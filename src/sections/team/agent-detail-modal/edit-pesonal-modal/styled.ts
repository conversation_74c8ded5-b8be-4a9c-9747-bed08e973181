import styled from "styled-components";
import { RowItem } from "styles";
import { ButtonPrimary, ButtonSecondary } from "styles/buttons";
import { device } from "styles/media";

export const EditPersonalModalWrapper = styled.div`
  width: 85%;

  margin: auto;
  margin-top: 24px;
  margin-bottom: 100px;

  padding: 24px;

  background: #ffffff;
  border-radius: 16px;

  @media ${device.mobile} {
    padding: 16px;
    margin-top: 44px;
    width: calc(100% - 32px);

    :not(:last-child) {
      border: none;
    }
  }
`;
export const EditPersonalModalItem = styled.div`
  :not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};
    margin-bottom: 16px;
  }

  .${RowItem.styledComponentId} {
    margin-top: 24px;
  }

  @media ${device.mobile} {
    :not(:last-child) {
      padding-bottom: 16px;
    }
  }
`;

export const InfoPersonal = styled.div`
  width: 85%;

  display: flex;
  align-items: center;
  background: #ffffff;

  h6 {
    margin-left: 8px;
  }
`;

export const Tooltip = styled.div`
  border-radius: 4px;

  background: ${({ theme }) => theme.color.status.primary_5};
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  transform: translateX(31px);
`;

export const Footer = styled.div`
  width: 100%;
  padding: 16px 32px;

  z-index: 1;
  background: white;

  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  justify-content: flex-end;

  box-shadow: 0px -1px 0px #dbdfe1;

  .${ButtonSecondary.styledComponentId}, .${ButtonPrimary.styledComponentId} {
    height: 52px;
    width: 100%;
    max-width: 200px;

    margin-left: 24px;
  }

  @media ${device.mobile} {
    .${ButtonSecondary.styledComponentId} {
      max-width: 400px;
      width: 50%;
      margin-left: 0;
    }

    .${ButtonPrimary.styledComponentId} {
      max-width: 400px;
      width: 50%;
    }
  }
`;
