/* eslint-disable react-hooks/exhaustive-deps */
import { ERROR_API_MESSAGE } from "@constants/message";
import {
  ListRegisTrainingData,
  ListRegisTrainingOutput,
  RegisAndCancelInput,
  TrainingClassSearchInput,
} from "@custom-types/academy";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { ErrorPolicy } from "@custom-types/policy-tracking";
import { getAllTrainingClasses, saveRegisChange } from "api/academy";
import { Alert } from "components/alert";
import CheckBox from "components/checkbox";
import { CheckBoxWhiteWrapper } from "components/checkbox/styled";
import ConfirmModal from "components/confirm-modal";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import LoadingSection from "components/loading";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { formatValueTable } from "services/format-value";
import { getDateFrom, getDateTo, handleCheckValidateMonthForm } from "services/untils";
import { RowItem } from "styles";
import { ButtonPrimary } from "styles/buttons";
import { ClassRegisterWrapper, FooterClass } from "./styled";

interface ClassRegisterProps {}

interface ClassFormSearch {
  classId: string;
  className: string;
  fromDate: string;
  toDate: string;
}

const config: TableConfig[] = [
  { key: "0", show: true, type: "function", label: "Chọn", textAlign: "center", hideMobile: true },
  { key: "orderNo", show: true, type: "string", label: "STT", textAlign: "center", hideMobile: true },
  { key: "classId", show: true, type: "string", label: "Mã lớp" },
  { key: "className", show: true, type: "string", label: "Tên Lớp" },
  { key: "detail", show: true, type: "string", label: "Giới thiệu sơ lược lớp học" },
  { key: "dayStart", show: true, type: "date", label: "Ngày bắt đầu" },
  { key: "dayEnd", show: false, type: "date", label: "Ngày kết thúc" },
];

const ClassRegister = ({}: ClassRegisterProps) => {
  const [formSearch, setFormSearch] = useState<ClassFormSearch>({
    classId: "",
    className: "",
    fromDate: getDateFrom(),
    toDate: getDateTo(),
  });

  const [error, setError] = useState<ErrorPolicy>({
    fromDate: null,
    toDate: null,
  });
  const [data, setData] = useState<ListRegisTrainingData[]>([]);

  const [showConfirm, setShowConfirm] = useState(false);
  const [dataUnCheck, setDataUnCheck] = useState<ListRegisTrainingData>(null);
  const [isUpdate, setIsUpdate] = useState(false);
  const [isTrigger, setIsTrigger] = useState(false);

  const {
    user: { username },
    loading: { getListTrainingLoading },
  } = useAppSelector((state) => state.rootReducer);

  //api
  const actionGetListTraining = useActionApi<TrainingClassSearchInput, ListRegisTrainingOutput>(getAllTrainingClasses);
  const actionSaveListTraining = useActionApi<RegisAndCancelInput, any>(saveRegisChange);

  const formatValueTableClass = (d: ListRegisTrainingData, config: TableConfig, index: number) => {
    switch (config.key) {
      case "0":
        return <CheckBox onChange={() => handleMultipleSelect(d)} checked={d.isRegis === "Y"} />;
      case "orderNo":
        return index + 1;
      case "dayStart":
        return moment(d[config.key]).format("DD/MM/YYYY") + " - " + d["hourStart"];
      case "dayEnd":
        return moment(d[config.key]).format("DD/MM/YYYY") + " - " + d["hourEnd"];
      default:
        return formatValueTable(d, config);
    }
  };

  const renderFuncHeaderMobile = (d: ListRegisTrainingData) => {
    return (
      <CheckBoxWhiteWrapper checked={d.isRegis === "Y"}>
        <CheckBox isCheckedWhite onChange={() => handleMultipleSelect(d)} checked={d.isRegis === "Y"} />
      </CheckBoxWhiteWrapper>
    );
  };

  const dataTable: DataTable[][] = useMemo(
    () =>
      data?.map((d, index) =>
        config.map((config) => ({
          config: config,
          node: formatValueTableClass(d, config, index),
          originData: get(d, [config.key]),
          renderFuncHeaderMobile: config.type === "function" && renderFuncHeaderMobile(d),
        }))
      ),
    [data, config]
  );

  const handleChangeData = (name: string, value: any) => {
    setFormSearch((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromDate" || name === "toDate") {
      const newError = handleCheckValidateMonthForm(name, value, formSearch.fromDate);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const onBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleCheckValidateMonthForm(name, formSearch.toDate, valueDateFrom);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  const handleSubmit = () => {
    if (username) {
      actionGetListTraining({
        body: {
          agentId: username,
          classId: formSearch.classId,
          className: formSearch.className,
          fromDate: moment(formSearch.fromDate, "DD/MM/YYYY").format("YYYY-MM-DD"),
          toDate: moment(formSearch.toDate, "DD/MM/YYYY").format("YYYY-MM-DD"),
        },
        loading: {
          type: "local",
          name: "getListTrainingLoading",
        },
      })
        .then(({ data }) => {
          setData(data.listData);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
  };

  const handleMultipleSelect = (dataCheck: ListRegisTrainingData) => {
    if (dataCheck.isRegis === "Y") {
      setDataUnCheck(dataCheck);
      setShowConfirm(true);
    } else {
      setData((prevData) =>
        prevData.map((item) => (item.classId === dataCheck.classId ? { ...item, isRegis: "Y" } : item))
      );
      setIsTrigger(true);
    }
  };

  const handleSaveChange = () => {
    actionSaveListTraining({
      body: {
        agentId: username,
        listClassIdsRegis: data?.filter((item) => item.isRegis === "Y").map((list) => list.classId),
        listClassIdsCancel: data?.filter((item) => item.isRegis === "N").map((list) => list.classId),
      },
      loading: {
        type: "global",
        name: "getSaveTrainingLoading",
      },
    })
      .then(({ data }) => {
        if (data) {
          Alert("SUCCESSFUL", "Bạn đã cập nhật thông tin đăng ký lớp học thành công");
          setIsUpdate(true);
          setIsTrigger(false);
        } else {
          Alert("ERROR", "Cập nhật thông tin đăng ký lớp học thất bại");
          setIsUpdate(false);
        }
      })
      .catch((err) => {
        console.log(err);
        Alert("ERROR", ERROR_API_MESSAGE);
        setIsUpdate(false);
      });
  };

  const handleConfirmCancel = () => {
    setData((prevData) =>
      prevData.map((item) => (item.classId === dataUnCheck.classId ? { ...item, isRegis: "N" } : item))
    );
    setDataUnCheck(null);
    setShowConfirm(false);
    setIsTrigger(true);
  };

  useEffect(() => {
    if (username || isUpdate) {
      actionGetListTraining({
        body: {
          agentId: username,
          classId: "",
          className: "",
          fromDate: getDateFrom("YYYY-MM-DD"),
          toDate: getDateTo("YYYY-MM-DD"),
        },
        loading: {
          type: "local",
          name: "getListTrainingLoading",
        },
      })
        .then(({ data }) => {
          setData(data?.listData);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
  }, [username, isUpdate]);

  return (
    <ClassRegisterWrapper>
      <FormSearchTemplate
        title="Thông tin"
        onSubmit={handleSubmit}
        disableSubmit={Boolean(error.fromDate) || Boolean(error.toDate)}
        loading={Boolean(getListTrainingLoading)}
      >
        <RowItem>
          <InputFieldSet
            name="classId"
            placeholder="Mã lớp"
            value={formSearch.classId}
            onChange={(e) => handleChangeData("classId", e.target.value)}
          />
          <InputFieldSet
            name="className"
            placeholder="Tên lớp"
            value={formSearch.className}
            onChange={(e) => handleChangeData("className", e.target.value)}
          />
        </RowItem>
        <RowItem>
          <DateInput
            placeholder="Từ ngày"
            value={formSearch.fromDate}
            error={error.fromDate}
            onBlur={(value) => onBlurData("toDate", value)}
            onChange={(value) => handleChangeData("fromDate", value)}
          />
          <DateInput
            placeholder="Đến ngày"
            value={formSearch.toDate}
            error={error.toDate}
            onChange={(value) => handleChangeData("toDate", value)}
          />
        </RowItem>
      </FormSearchTemplate>

      {Boolean(getListTrainingLoading) ? (
        <LoadingSection loading={Boolean(getListTrainingLoading)} />
      ) : (
        <>
          <h6 className="h7">
            Tổng cộng <span className="color-primary">{data?.length}</span> kết quả đã tìm thấy
          </h6>
          <p className="body-5  mb-16">
            Tổng số lớp đăng ký:{" "}
            <span className="h8 color-primary">{data?.filter((i) => i.isRegis === "Y").length}</span>
          </p>
          <Table
            data={dataTable}
            config={config}
            showOrderNo={false}
            showConfig={true}
            showPagination={true}
            // loading={}
          />
          <TableMobile data={dataTable} config={config} showCollapse={true} />
        </>
      )}
      <ConfirmModal
        content="Bạn có chắc chắn là huỷ đăng ký lớp học này không?"
        title="Xác nhận"
        onConfirm={handleConfirmCancel}
        show={showConfirm}
        onCancel={() => setShowConfirm(false)}
        confirmTitle="Xác nhận"
      />
      <FooterClass>
        <ButtonPrimary onClick={handleSaveChange} disabled={Boolean(getListTrainingLoading) || !isTrigger}>
          Cập nhật thông tin đăng ký
        </ButtonPrimary>
      </FooterClass>
    </ClassRegisterWrapper>
  );
};

export default ClassRegister;
